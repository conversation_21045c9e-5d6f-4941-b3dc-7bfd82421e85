import 'dart:async';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:palette_generator/palette_generator.dart';

class TakeBgColor {

  static Future<void> getImageDominantColor(String imageUrl,{bool reOld = false}) async {
    final oldColor = ui.Color.fromRGBO(12, 12, 12, 1);
    
    if (reOld) {
      homCon.playBgColor.value = oldColor;
      return;
    }

    if (imageUrl == '') {
      homCon.playBgColor.value = oldColor;
      return;
    }
    
    ui.Image? image;
    try {
      final completer = Completer<ui.Image>();
      final imageProvider = NetworkImage(imageUrl);
      imageProvider.resolve(const ImageConfiguration()).addListener(
        ImageStreamListener((info, _) {
          completer.complete(info.image);
        }),
      );
      image = await completer.future;
    } catch (e) {
      homCon.playBgColor.value = oldColor;
      return;
    }
    PaletteGenerator paletteGenerator = await PaletteGenerator.fromImage(image);
    final dominantColor = paletteGenerator.dominantColor?.color ?? oldColor;
    homCon.playBgColor.value = dominantColor;
  }

  static double getBrightness(Color color) {
    final r = color.r;
    final g = color.g;
    final b = color.b;
    return (r + g + b) / 3;
  }
}