import 'dart:io';
import 'dart:typed_data';
import 'package:gocontrol/log.dart';
import 'package:nsd/nsd.dart';

class WiFiApi {
  late Discovery discovery;

  // 开始搜索附近指定网络服务
  static void startScan(callback, {
      String serviceType = '_linkplay._tcp', // 服务类型
      int serverPort = 8899 // 服务端口
    }) async {
    final discovery = await startDiscovery(serviceType);
    discovery.addServiceListener((service, status) async {
      if (status == ServiceStatus.found) {
        String? host = service.host;
        if (host != null) {
          String ip = await resolveDeviceIP(host);
          if (ip != '-1') callback(ip);
        }
      }
    });
  }

  /// 返回设备IP地址
  static Future<String> resolveDeviceIP(String hostname) async {
    try {
      final addresses = await InternetAddress.lookup(hostname);
      // 获取所有网络接口信息
      for (var address in addresses) {
        if (address.type == InternetAddressType.IPv4) {
          return address.address;
        }
      }
      return '-1';
    } catch (e) {
      Log.e('处理ip结果失败: $e');
      return '-1';
    }
  }

  /// 发送消息给Socket
  static void sendMsg(Socket sokcet, String msg) {
    try {
      Future.microtask(() => sokcet.add(getData(msg)));
    } catch (e) {
      Log.e('发送失败');
    }
  }

  // 处理监听后返回的data数据
  static Map<String, dynamic> parseData(List<int> data) {
    // 转换字节高低位
    int bytesToIntLittleEndian(Uint8List bytes) {
      try {
        int result = 0;
        for (int i = 0; i < bytes.length; i++) {
          result += bytes[i] << (8 * i);
        }
        return result;
      } catch (e) {
        return 0;
      }
    }

    // 解析数据包
    try {
      Map<String, dynamic> result = {};
      Uint8List header = Uint8List.fromList(data.sublist(0, 4));
      result['header'] = header;
      Uint8List lengthBytes = Uint8List.fromList(data.sublist(4, 8));
      int length = bytesToIntLittleEndian(lengthBytes);
      result['length'] = length;
      Uint8List checksumBytes = Uint8List.fromList(data.sublist(8, 12));
      int checksum = bytesToIntLittleEndian(checksumBytes);
      result['checksum'] = checksum;
      Uint8List reserved = Uint8List.fromList(data.sublist(12, 20));
      result['reserved'] = reserved;
      Uint8List payload = Uint8List.fromList(data.sublist(20));
      result['payload'] = payload;
      return result;
    } catch (err) {
      Log.e('解析数据时出现错误，错误信息:$err,所以返回空Map对象{}');
      return {};
    }
  }

  // 将发送的数据转换成接口需要的格式
  static List<int> getData(String payload) {
    // 固定 header
    List<int> header = [0x18, 0x96, 0x18, 0x20];
    // 计算 payload 内容的长度
    int payloadLength = payload.length;
    // 计算 checksum，将 payload 中每个字节的 ASCII 值相加
    int checksum = 0;
    for (int i = 0; i < payload.length; i++) {
      checksum += payload.codeUnitAt(i);
    }
    // 构建 length 的 little endian 格式
    List<int> lengthBytes = [];
    for (int i = 0; i < 4; i++) {
      lengthBytes.add(payloadLength & 0xFF);
      payloadLength >>= 8;
    }
    // 构建 checksum 的 little endian 格式
    List<int> checksumBytes = [];
    for (int i = 0; i < 4; i++) {
      checksumBytes.add(checksum & 0xFF);
      checksum >>= 8;
    }
    // 固定 reserved
    List<int> reserved = [0, 0, 0, 0, 0, 0, 0, 0];
    // 将所有部分合并成整数列表 并返回
    return [
      ...header,
      ...lengthBytes,
      ...checksumBytes,
      ...reserved,
      ...payload.codeUnits
    ];
  }
}

// 创建播放列表
class CreateQueue {
  static String url(String ip) => 'http://$ip:59152/upnp/control/$sub';
  static String sub = 'PlayQueue1';
  static String srv = 'urn:schemas-wiimu-com:service:PlayQueue:1';
  static String act = 'CreateQueue';
  static String envs =
      '<s:Envelope s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body>';
  static String enve = '</s:Body></s:Envelope>';
  static String dat(String queueContext) {
    Log.w(
        '$envs<u:$act xmlns:u="$srv"><QueueContext>$queueContext</QueueContext><QueuePolicy>1</QueuePolicy></u:$act>$enve');
    return '$envs<u:$act xmlns:u="$srv"><QueueContext>$queueContext</QueueContext><QueuePolicy>1</QueuePolicy></u:$act>$enve';
  }

  static String hsa = '"$srv#$act"';
  static String queueContext(Map item, String radioName) {
    Log.d('------$item');
    String title = item['title'];
    String listName = title.replaceAll(RegExp(r'\s+'), '_');
    queueName = listName;
    return '&lt;?xml version=&quot;1.0&quot;?&gt;&lt;PlayList&gt;&lt;ListName&gt;$listName&lt;/ListName&gt;&lt;ListInfo&gt;&lt;Radio&gt;0&lt;/Radio&gt;&lt;SourceName&gt;MyUPnPServer&lt;/SourceName&gt;&lt;PicUrl&gt;&lt;/PicUrl&gt;&lt;TrackNumber&gt;1&lt;/TrackNumber&gt;&lt;SearchUrl&gt;&lt;/SearchUrl&gt;&lt;Quality&gt;0&lt;/Quality&gt;&lt;/ListInfo&gt;&lt;Tracks&gt;&lt;Track1&gt;&lt;URL&gt;${item['play_url']}&lt;/URL&gt;&lt;Source&gt;MyUPnPServer&lt;/Source&gt;&lt;Key&gt;&lt;/Key&gt;&lt;Id&gt;&lt;/Id&gt;&lt;Metadata&gt;&amp;lt;?xml version=&amp;quot;1.0&amp;quot; encoding=&amp;quot;UTF-8&amp;quot;?&amp;gt;&amp;lt;DIDL-Lite xmlns:dc=&amp;quot;http://purl.org/dc/elements/1.1/&amp;quot; xmlns:upnp=&amp;quot;urn:schemas-upnp-org:metadata-1-0/upnp/&amp;quot; xmlns:song=&amp;quot;www.wiimu.com/song/&amp;quot; xmlns:custom=&amp;quot;www.wiimu.com/custom/&amp;quot; xmlns=&amp;quot;urn:schemas-upnp-org:metadata-1-0/DIDL-Lite/&amp;quot;&amp;gt;&amp;lt;upnp:class&amp;gt;object.item.audioItem.musicTrack&amp;lt;/upnp:class&amp;gt;&amp;lt;item&amp;gt;&amp;lt;song:id&amp;gt;&amp;lt;/song:id&amp;gt;&amp;lt;song:albumid&amp;gt;&amp;lt;/song:albumid&amp;gt;&amp;lt;song:singerid&amp;gt;&amp;lt;/song:singerid&amp;gt;&amp;lt;dc:title&amp;gt;${item['title']}&amp;lt;/dc:title&amp;gt;&amp;lt;upnp:artist&amp;gt;${item['user_name']}&amp;lt;/upnp:artist&amp;gt;&amp;lt;upnp:album&amp;gt;$radioName&amp;lt;/upnp:album&amp;gt;&amp;lt;res protocolInfo=&amp;quot;http-get:*:audio/mpeg:DLNA.ORG_PN=MP3;DLNA.ORG_OP=01;&amp;quot; duration=&amp;quot;0&amp;quot;&amp;gt;${item['play_url']}&amp;lt;/res&amp;gt;&amp;lt;upnp:albumArtURI&amp;gt;${item['image_url']}&amp;lt;/upnp:albumArtURI&amp;gt;&amp;lt;/item&amp;gt;&amp;lt;/DIDL-Lite&amp;gt;&lt;/Metadata&gt;&lt;/Track1&gt;&lt;/Tracks&gt;&lt;/PlayList&gt;';
  }
  

  static String queueName = '';
}

// 播放歌曲
class PlayQueue {
  static String url(String ip) => 'http://$ip:59152/upnp/control/$sub';
  static String sub = 'PlayQueue1';
  static String srv = 'urn:schemas-wiimu-com:service:PlayQueue:1';
  static String act = 'PlayQueueWithIndex';
  static String envs =
      '<s:Envelope s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body>';
  static String enve = '</s:Body></s:Envelope>';
  static String dat(String queueName) {
    return '$envs<u:$act xmlns:u="$srv"><QueueName>$queueName</QueueName><Index>1</Index></u:$act>$enve';
  }

  static String hsa = '"$srv#$act"';
}

class WPlayQueueWithIndex {
  static String url(String ip) {
    return 'http://$ip:59152/upnp/control/$sub';
  }

  static String sub = 'PlayQueue1';
  static String srv = 'urn:schemas-wiimu-com:service:PlayQueue:1';
  static String act = 'PlayQueueWithIndex';
  // static String msg = '<InstanceID>0</InstanceID><Speed>1</Speed>';
  static String envs =
      '<s:Envelope s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body>';
  static String enve = '</s:Body></s:Envelope>';
  static String dat(String queueName, int queueIndex) {
    return '$envs<u:$act xmlns:u="$srv"><QueueName>$queueName</QueueName><Index>$queueIndex</Index></u:$act>$enve';
  }

  static String hsa = '"$srv#$act"';
}

class WPrevious {
  static String url(String ip) => 'http://$ip:59152/upnp/control/$sub';
  static String sub = 'rendertransport1';
  static String srv = 'urn:schemas-upnp-org:service:AVTransport:1';
  static String act = 'Previous';
  static String msg = '<InstanceID>0</InstanceID>';
  static String envs =
      '<s:Envelope s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body>';
  static String enve = '</s:Body></s:Envelope>';
  static String dat = '$envs<u:$act xmlns:u="$srv">$msg</u:$act>$enve';
  static String hsa = '"$srv#$act"';
}

class WNext {
  static String url(String ip) {
    return 'http://$ip:59152/upnp/control/$sub';
  }

  static String sub = 'rendertransport1';
  static String srv = 'urn:schemas-upnp-org:service:AVTransport:1';
  static String act = 'Next';
  static String msg = '<InstanceID>0</InstanceID>';
  static String envs =
      '<s:Envelope s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body>';
  static String enve = '</s:Body></s:Envelope>';
  static String dat = '$envs<u:$act xmlns:u="$srv">$msg</u:$act>$enve';
  static String hsa = '"$srv#$act"';
}

// 获取info信息
class WGetinfo {
  static String url(String ip) {
    return 'http://$ip:59152/upnp/control/$sub';
  }

  static String sub = 'rendertransport1';
  static String srv = 'urn:schemas-upnp-org:service:AVTransport:1';
  static String act = 'GetInfoEx';
  static String msg = '<InstanceID>0</InstanceID>';
  static String envs =
      '<s:Envelope s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body>';
  static String enve = '</s:Body></s:Envelope>';
  static String dat = '$envs<u:$act xmlns:u="$srv">$msg</u:$act>$enve';
  static String hsa = '"$srv#$act"';
}

class WgetMediaInfo {
  static String url(String ip) {
    return 'http://$ip:59152/upnp/control/$sub';
  }

  static String sub = 'rendertransport1';
  static String srv = 'urn:schemas-upnp-org:service:AVTransport:1';
  static String act = 'GetMediaInfo';
  static String msg = '<InstanceID>0</InstanceID>';
  static String envs =
      '<s:Envelope s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body>';
  static String enve = '</s:Body></s:Envelope>';
  static String dat = '$envs<u:$act xmlns:u="$srv">$msg</u:$act>$enve';
  static String hsa = '"$srv#$act"';
}

class WBrowseQueue {
  static String url(String ip) {
    return 'http://$ip:59152/upnp/control/$sub';
  }

  static String sub = 'PlayQueue1';
  static String srv = 'urn:schemas-wiimu-com:service:PlayQueue:1';
  static String act = 'BrowseQueue';
  static String envs =
      '<s:Envelope s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body>';
  static String enve = '</s:Body></s:Envelope>';
  static String dat({String queueName = ''}) {
    if (queueName == '') queueName = 'CurrentQueue';
    return '$envs<u:$act xmlns:u="$srv">${'<QueueName>$queueName</QueueName><SkipQueue>0</SkipQueue>'}</u:$act>$enve';
  }

  static String hsa = '"$srv#$act"';
}

class WCreateQueue {
  static String url(String ip) => 'http://$ip:59152/upnp/control/$sub';

  static String sub = 'PlayQueue1';
  static String srv = 'urn:schemas-wiimu-com:service:PlayQueue:1';
  static String act = 'CreateQueue';
  static String envs = '<s:Envelope s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body>';
  static String enve = '</s:Body></s:Envelope>';
  static String dat({String tracks = ''}) {
    return '$envs<u:$act xmlns:u="$srv"><QueueContext>$tracks</QueueContext><QueuePolicy>1</QueuePolicy></u:$act>$enve';
  }


  static String hsa = '"$srv#$act"';
}
