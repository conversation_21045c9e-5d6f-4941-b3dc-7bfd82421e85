import 'dart:async';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:gocontrol/core/permission.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/a31/a31.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';

// 处理用户返回app的数据
class AppObserver with WidgetsBindingObserver {

  bool lifeSate = true;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if(Platform.isMacOS) return;
    // 返回后台
    if (state == AppLifecycleState.resumed && lifeSate == false) {
      lifeSate = true;
      //  如果没有权限先查找一下
      if (AppPermission.isGranted == false) {
        AppPermission.init();
        return;
      }
      networkDeviceReconnect();
    // 退出到后台 
    } else if (state == AppLifecycleState.hidden && lifeSate == true) {
      lifeSate = false;
      networkDeviceClose();
    }
  }

  // 如果有socket设备断开，就重新连接
  void networkDeviceReconnect() async {
    if (homCon.adevices.isEmpty) return;

    for (var device in homCon.adevices) {
      if (device is A31) device.connectSocket(reconnect: true);
      if (device is Libre) {
        if (device.updateState.value != 0) return Log.d('${device.name.value} 正在升级,不重连socket。');
        device.initNetwork(
          rev: true
        );
      }
    }
  }

  // 如果有socket设备断开，就重新连接
  void networkDeviceClose() async {

    for (var device in homCon.adevices) {
      if (device is A31) device.closeSocket();
      if (device is Libre) {
        if (device.updateState.value != 0) return Log.d('${device.name.value} 正在升级,不断开socket。');
        Log.d('关闭 ${device.name.value} 的socket');
        _closeSocket(device.tcpSocket);
        device.tcpSocket = null;
        _cancelStream(device.tcpSubStream);
        device.tcpSubStream = null;
        _closeSocket(device.luciSocket);
        device.luciSocket = null;
        _cancelStream(device.streamLuci);
        device.streamLuci = null;
        device.luciSocketState.value = false;
      }
    }
  }

  // 封装关闭socket的方法
  void _closeSocket(Socket? socket) {
    if (socket != null) {
      socket.close();
    }
  }

  // 封装取消流的方法
  void _cancelStream(StreamSubscription? stream) {
    if (stream != null) {
      stream.cancel();
    }
  }
}

