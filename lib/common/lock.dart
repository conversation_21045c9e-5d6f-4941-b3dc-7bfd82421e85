import 'dart:async';

/// 互斥锁实现类，用于控制异步操作的顺序执行
class Lock {
  Completer<void>? _completer;
  
  /// 判断锁是否被占用（true表示有操作正在执行）
  bool get isUsing => _completer != null;

  /// 在互斥锁保护下执行异步操作
  /// [callback]: 需要受保护执行的异步回调函数
  /// 返回：回调函数的执行结果
  Future<T> mutex<T>(Future<T> Function() callback) async {
    await _lock();
    try {
      return await callback();
    } finally {
      unlock();
    }
  }

  /// 等待当前操作完成（必须在锁被占用时调用）
  Future<void> waitDone() {
    assert(_completer != null);
    return _completer!.future;
  }

  /// 获取锁（内部方法）
  Future<void> _lock() async {
    while (_completer != null) {
      await _completer!.future;
    }
    _completer = Completer<void>();
    return;
  }

  /// 释放锁
  /// [StateError]: 当重复释放锁时抛出
  void unlock() {
    if (_completer == null) {
      throw StateError('锁状态错误');
    }
    _completer!.complete();
    _completer = null;
  }
}