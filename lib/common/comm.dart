
import 'dart:convert';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:http/http.dart' as http;
import 'package:gocontrol/components/go_icon.dart';
import 'package:xml2json/xml2json.dart';

class Comm {
  static List<String> devList = [
    'UP2STREAM_AMP_2P1',
    'UP2STREAM_MINI_V3',
    'UP2STREAM_MINI_V4',
    'UP2STREAM_PRO_V3',
    'UP2STREAM_PRO_V4',
    'UP2STREAM_HDDAC',
    'UP2STREAM_AMP_V3',
    'UP2STREAM_AMP_V4',
    'ARYLIC_A30',
    'ARYLIC_A50',
    'ARYLIC_S50A',
    'ARYLIC_S50',
    'ARYLIC_V20',
    'RAKOSO_M400',
    'RAKOSO_MA400',
    'SA100',
    'A100_MCU',
    'RAKOSO_HA400',
    'RAKOSO_H400',
    'WBC65',
    'UP2CAST'
  ];
  
  // 十六进制字符串转成普通字符串
  static String hexStrToString(String hex) {
    List<int> bytes = [];
    try {
      for (int i = 0; i < hex.length; i += 2) {
        String hexSubstring = hex.substring(i, i + 2);
        int byte = int.parse(hexSubstring, radix: 16);
        bytes.add(byte);
      }
      return utf8.decode(bytes);
    } catch(e) {
      return '';
    }
  }

  // 字符串转hex
  static String stringToHex(String input) {
    List<int> codeUnits = input.codeUnits;
    // 将每个ASCII码转换为两位十六进制字符串
    List<String> hexList = codeUnits.map((int code) => code.toRadixString(16).toUpperCase().padLeft(2, '0')).toList();
    // 将十六进制字符串列表合并为一个字符串
    String hexString = hexList.join('');
    return hexString;
  }

  // 转换输入源分发到各设备
  static void sendSource(DeviceBase device,String source){
    for (var sr in device.sourceInputList) {
      if (sr['id'] == source) return;
    }
    switch(source){
      case 'NET':
        device.sourceInputList.add({
          'id': source,
          'name': 'Music',
          'icon': GoIcons.srcWiFi
        });
      break;
      case 'BT':
        device.sourceInputList.add({
          'id': source,
          'name': 'BT',
          'icon': GoIcons.srcBT
        });
      break;
      case 'LINE-IN':
        device.sourceInputList.add({
          'id': source,
          'name': 'AUX',
          'icon': GoIcons.srcAux
        });
      break;
      case 'LINE-IN2':
        device.sourceInputList.add({
          'id': source,
          'name': 'AUX2',
          'icon': GoIcons.srcAux
        });
      break;
      case 'PHONO':
        device.sourceInputList.add({
          'id': source,
          'name': 'PHONO',
          'icon': GoIcons.srcPhono
        });
      break;
      case 'OPT':
        device.sourceInputList.add({
          'id': source,
          'name': 'OPT',
          'icon': GoIcons.srcOPT
        });
      break;
      case 'HDMI':
        device.sourceInputList.add({
          'id': source,
          'name': 'HDMI',
          'icon': GoIcons.srcARC
        });
      break;
      case 'USB':
        device.sourceInputList.add({
          'id': source,
          'name': 'USB',
          'icon': GoIcons.srcUSB
        });
      break;
      case 'USBPLAY':
        device.sourceInputList.add({
          'id': source,
          'name': 'USB',
          'icon': GoIcons.srcUSB
        });
      break;
      case 'USBDAC':
        device.sourceInputList.add({
          'id': source,
          'name': 'DAC',
          'icon': GoIcons.srcDAC
        });
      break;
      case 'COAX':
        device.sourceInputList.add({
          'id': source,
          'name': 'COAX',
          'icon': GoIcons.srcCOAX
        });
      break;
      case 'FM':
        device.sourceInputList.add({
          'id': source,
          'name': 'FM',
          'icon': GoIcons.srcRadio
        });
      break;
      case 'DAB':
        device.sourceInputList.add({
          'id': source,
          'name': 'DAB',
          'icon': GoIcons.srcRadio
        });
      break;
      case 'AURA':
        device.sourceInputList.add({
          'id': source,
          'name': 'AURA',
          'icon': GoIcons.leaudio
        });
      break;
    }
  }

  // 发送请求
  static Future<Map> postData({required url,required hsa,required dat}) async {
    try{
      final response = await http.post(
        Uri.parse(url),
        headers: <String, String>{
          'SOAPACTION': hsa,
          'Content-Type': 'text/xml;charset="utf-8"',
        },
        body: dat,
      );
      if (response.statusCode == 200) {
        String modifiedText = response.body.replaceAll("\r", "").replaceAll("\n", "");
        Map data = translateInfo(modifiedText)['s:Envelope']['s:Body'];
        return data;
      } else {
        Log.e(response.statusCode);
        return {};
      }
    }catch(e){
      Log.e('请求失败,$e');
      return {};
    }
  }

  // 请求get
  static Future<String> requestGet({required String ip,required String command}) async {
    try{
      final response = await http.get(Uri.parse('http://$ip/$command'));
      if(response.statusCode == 200){
        return response.body;
      }else{
        Log.e('get失败');
        return '';
      }
    }catch(e){
      Log.e('get发生错误:$e');
      return '';
    }
  }

  // 转换xml
  static Map translateInfo(data){
    final Xml2Json xml2json = Xml2Json();
    xml2json.parse(data);
    return json.decode(xml2json.toParker());
  }

  static List<int> findOnesInHex(int hexValue) {
    List<int> onesList = [];
    String binaryString = hexValue.toRadixString(2); // 将十六进制转换为二进制字符串
    for (int i = 0; i < binaryString.length; i++) {
      if (binaryString[i] == '1') {
        onesList.add(binaryString.length - i); // 添加1的位数到列表
      }
    }
    return onesList;
  }

  static Future<void> queryAndExecute(Future<bool> Function() judge, Future<void> Function() execute, {Future<void> Function()? executeIfNoResult}) async {
    // 先立即执行一次操作
    await execute();
    await Future.delayed(const Duration(seconds: 1));
    // 如果第一次执行返回 true，直接退出
    if (await judge()) return;
    // await Future.delayed(const Duration(milliseconds: 500));
    for (int i = 0; i < 2; i++) {
      if (await judge()) break;
      await execute();
      await Future.delayed(const Duration(seconds: 1));
    }
    // 如果最后一次查询仍没有结果，执行 executeIfNoResult
    if (!await judge() && executeIfNoResult != null) {
      await executeIfNoResult();
    }
  }
  // 转换String成bytes
  static List<int> convertStringToBytes(String input) {
    List<int> byteList = [];
    // 检查是否是十六进制字符串
    if (RegExp(r'^[0-9a-fA-F]+$').hasMatch(input)) {
      for (int i = 0; i < input.length; i += 2) {
        String hex = input.substring(i, i + 2);
        int byte = int.parse(hex, radix: 16);
        byteList.add(byte);
      }
    } else {
      // 如果不是十六进制字符串，将字符串转换为UTF-8编码的字节
      List<int> utf8Bytes = utf8.encode(input);
      byteList.addAll(utf8Bytes);
    }
    return byteList;
  }
  // 高低位转换
  static List<int> calculateLengthBytes(String data) {
    int length = data.length;
    List<int> lengthBytes = [length & 0xFF,(length >> 8) & 0xFF];
    return lengthBytes;
  }
   
  // 时间文字转换成毫秒
  static double timeStringToSeconds(String timeString) {
    List<String> timeComponents = timeString.split(':');
    double hours = double.parse(timeComponents[0]);
    double minutes = double.parse(timeComponents[1]);
    double seconds = double.parse(timeComponents[2]);
    return hours * 3600 + minutes * 60 + seconds;
  }
}