import 'dart:async';
import 'dart:io';

import 'package:gocontrol/common/lock.dart';
import 'package:gocontrol/log.dart';
import 'package:intranet_ip/intranet_ip.dart';

class IpV4AndIpV6Tool {
  static final _lock = Lock();

  static String? _localIpv4;

  static FutureOr<String> get localIpv4 {
    if (_localIpv4 == null) {
      return Future<String>.sync(() async {
        if (_lock.isUsing) {
          await _lock.waitDone();
          assert(_localIpv4 != null);
          return _localIpv4!;
        } else {
          return _lock.mutex(() async {
            try {
              String localIp = '';
              await getIpv4AndIpV6Addresses((ip) => localIp = ip);
              _localIpv4 = localIp;
            } catch (_) {}
            return _localIpv4!;
          });
        }
      });
    }
    return _localIpv4!;
  }

  static Future<void> getIpv4AndIpV6Addresses(
      Function(String ipv4Address) didClickConfirmBtnAction) async {
    String tempIpv4 = "";
    if (Platform.isWindows || Platform.isAndroid) {
      try {
        final ip = await intranetIpv4();
        didClickConfirmBtnAction(ip.address);
      } catch (_) {}
    } else {
      try {
        // 获取所有网络接口信息
        List<NetworkInterface> interfaces = await NetworkInterface.list(
          includeLoopback: true, // 是否包含回环接口
          includeLinkLocal: true, // 是否包含链路本地接口（例如IPv6的自动配置地址）。
          type: InternetAddressType.IPv4,
        );
        // 遍历所有网络接口
        for (var interface in interfaces) {
          Log.r('Interface name: ${interface.name}');
          //windows处理不一致

          // 遍历接口的地址
          for (var address in interface.addresses) {
            if (interface.name == 'en0' ||
                interface.name == 'en1' ||
                interface.name == 'en2') {
              if (address.address != '' || address.address.isNotEmpty) {
                tempIpv4 = address.address;
              }
            } else {
              continue;
            }
          }
        }
      } catch (e, stack) {
        Log.e('Failed to get IP addresses: $e $stack');
      } finally {
        didClickConfirmBtnAction(tempIpv4);
      }
    }
  }
}
