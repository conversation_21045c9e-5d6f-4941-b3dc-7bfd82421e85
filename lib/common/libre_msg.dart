import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gocontrol/api/api.dart';
import 'package:gocontrol/common/ble_api.dart';
import 'package:gocontrol/common/comm.dart';
import 'package:gocontrol/common/toast.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/routes/routes.dart';

class LibreMsg {
  static getBleMsg(List<int> event,Libre libre) async {
    // 事件id
    int eventId = event[1];
    // 数据内容
    List<int> data = event.sublist(4, event.length - 1);
    // 数据长度
    int dataLength = event[2] + (event[3] << 8);
    Log.r('设备名字 -- ${libre.device.platformName} 事件id -- $eventId || 数据内容 -- $data || ${utf8.decode(data)} || 数据长度 -- $dataLength');

    Log.w(event);

    // 退出待机模式
    if (eventId == 35) {
      if (data[0] == 1) {
        Log.r('退出待机模式成功(进行下一步操作)');
        libre.friendlyName();
      } else {
        // 推出设备已激活
        Log.r('退出待机模式失败');
      }
    }

    // 友好名称
    if(eventId == 5 && data.isNotEmpty){
      libre.name.value = utf8.decode(data);
    }

    // wifi列表数据
    if(eventId == 18 && data.isNotEmpty){
      libre.scanWifiList += data;
    }

    // wifi列表总长度
    if (eventId == 17 && data.isNotEmpty) {
      final len = utf8.decode(data);
      libre.scanWifiListLen = int.tryParse(len) ?? 0;
    }

    // wifi搜索结果
    if (eventId == 19) {
      try {
        if (libre.scanWifiListLen == 0) {
          throw Exception('Failed to obtain the list. Try again. (code: 1)');
        }

        if (libre.scanWifiList.length == libre.scanWifiListLen) {
          final list = utf8.decode(libre.scanWifiList);
          List items = json.decode(list.replaceAll('SSID', 'ssid'))['Items'];

          for (var item in items) {
            libre.wifiScanList.add(item);
          }
          
          await Future.delayed(const Duration(milliseconds: 500), () {
            libre.wifiSetupIndex.value = 3;
          });

        } else {
          throw Exception('长度不一致 (code: 2)');
        }
      } catch (e) {
        libre.scanWifiList.clear();
        libre.scanWifiListLen = 0;
        libre.wifiSetupIndex.value = 2;
        libre.getWifiScanList();
        Log.d('失败了 $e');
        // AppToast.show('Failed to obtain the list. Try again. (code: 3)');
      }
    }

    if (eventId == 23) {
      libre.name.value = libre.device.platformName.replaceAll(RegExp(r'-BLE'), '');
      Log.d('连接成功 ${libre.name.value}');
      homCon.libreSetUpName = libre;
      Log.d('设置当前homCon.libreSetUpName ${homCon.libreSetUpName}');
    }

    if (eventId == 24) {
      libre.wifiConnectResult.value = 2;
      libre.wifiSetupIndex.value = 6;
      BleApi.offScan();
      libre.disconnectBLE();

      Log.r('配网完成，当前名字 -- ${libre.name.value}');
      // 存入
      homCon.libreUpList.add(libre.name.value);

      // 封装重复的扫描逻辑
      await Future.delayed(const Duration(seconds: 7));
      Log.w('查找!!!');
      await ApiManage.luciApi.startScan((List<String> result) => homCon.watchForLibre(result));

      final List<Libre> theDevices = [];

      for (var dev in homCon.adevices) {
        if (dev is Libre) theDevices.add(dev);
      }

      if (theDevices.isNotEmpty) {
        final List<String> libreNames = theDevices.map((el) => el.name.value).toList();
        if (libreNames.contains(libre.name.value)) {
          Log.r('${libreNames.contains(libre.name.value)} ${libre.name.value} $libreNames');
          for (var el in theDevices) {
            el.needWakeUp = true;
          }
        }
      }

      // if (haveName) {
      //   final Libre myLibre = homCon.adevices.whereType<Libre>().firstWhere((el) => el.name.value == libre.name.value);
      //   myLibre.needWakeUp = true;
      // }

      
      // bool end = false;
      // // 执行4次扫描
      // for (int i = 0; i < 3; i++) {
      //   if (end) return;
      //   if (haveName) {
      //     final Libre myLibre = homCon.adevices.whereType<Libre>().firstWhere((el) => el.name.value == libre.name.value);
      //     myLibre.needWakeUp = true;
      //     end = true;
      //   }
      //   await performScan();
      // }

      // if (libre.wakeUpTimer != null) {
      //   libre.wakeUpTimer!.cancel();
      //   libre.wakeUpTimer = null;
      // }

      // int sec = 0;
      // libre.wakeUpTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      //   try {
      //     ApiManage.luciApi.startScan((List<String> result) => homCon.watchForLibre(result));
      //   } catch(_) {}

      //   Log.w('查找次数 $sec');
      //   if (have) {
      //     try {
      //       final String lname = homCon.libreSetUpName?.name.value ?? '';
      //       final Libre slb = homCon.adevices.whereType<Libre>().firstWhere((el) => el.name.value == lname);

      //       if (!slb.needWakeUp) {
      //         libre.needWakeUp = false;
      //         libre.wakeUpTimer?.cancel();
      //         libre.wakeUpTimer = null;
      //         libre.disconnectBLE();
      //       }
      //     } catch(e) {
      //       Log.e(e);
      //     }
      //     return;
      //   }

      //   if (sec == 5) {
      //     Get.back();
      //     libre.wakeUpTimer?.cancel();
      //     libre.wakeUpTimer = null;
      //     libre.disconnectBLE();
      //     AppToast.show('Find new firmware version timeout!');
      //     // showMyBottomSheet(
      //     //   height: 260.sp,
      //     //   title: 'Timeout',
      //     //   content: Container(
      //     //     alignment: Alignment.center,
      //     //     padding: EdgeInsets.symmetric(horizontal: 17.sp),
      //     //     child: AText(
      //     //       text: 'Find new firmware version timeout!',
      //     //       softWrap: true,
      //     //       color: themeBase.primaryColor.value,
      //     //       size: 15.sp,
      //     //     ),
      //     //   ),
      //     //   showCancel: false.obs,
      //     //   confirm: () => Get.back()
      //     // );
      //   }
      //   sec++;
      // });
    }
    
    if (eventId == 25 && data.isNotEmpty) {
      // 连接失败
      AppToast.show('wrong_password'.tr);
      libre.wifiSetupIndex.value = 3;
    }
    
  }

  static getMsg(String msg,Libre libre,List<int> buff) async {
    String str;
    try {
      str = msg.substring(msg.indexOf(':') + 1,msg.length - 1);
    } catch(e) {
      str = '';
    }
    Log.w('${libre.name.value}: $msg');
    
    if (msg.contains('VOL:')) {
      try {
        libre.volume.value = double.parse(str);
      }catch(_){}
    }

    if (msg.contains('VER:')) {
      Log.d('--------------------$msg');
      libre.version.value = str;
    }

    if (msg.contains('SRC:')) {
      libre.sendMsgToTcpSocket('BTS;');
      if(str == libre.sourceInput.value) return;
      libre.usbSongList.clear();

      if (libre.sourceInput.value != '') {
        homCon.playBgColor.value = Color.fromRGBO(12, 12, 12, 1);
        libre.totTalVal.value = 0.1;
        libre.playLengths.value = 0.0;
        libre.isPlaying.value = false;
        libre.songName.value = '';
        libre.songArtist.value = '';
        libre.songAlbum.value = '';
        libre.songImage.value = '';
        libre.mime.value = '';
        libre.sampleRate.value = '';
      }

      libre.newSourceInput.value = '';
      libre.swingSRC.value = false;
      libre.sourceInput.value = str;
      
      if (libre.timerLoops != null) {
        libre.timerLoops!.cancel();
        libre.timerLoops = null;
      }

      if (str == 'LINE-IN') {
        if (libre.mute.value) {
          libre.isPlaying.value = false;
        } else {
          libre.isPlaying.value = true;
        }
      }

      if (str == 'USBPLAY') {
        Future.delayed(const Duration(milliseconds: 1500),()=> libre.tcpSocket!.add('DSK:TOT;'.codeUnits));
      } else if(msg.contains('USB')) {

        Log.d('检测到USB模式，开始获取USB列表');
        libre.getSongListUSBforLUCI();
      } else {
        libre.usbNowSongItem.value = '';
        libre.usbPlayIndex.value = -1;
        libre.dataKey.value = 0;
        libre.dataList.clear();
        libre.dataSetList.clear();
        libre.dataTot.value = 0;
      }
    }

    if (msg.contains('DSK:TOT:')) {
      int num = int.parse(str.substring(4, str.length));
      if (num == 12288 || num == 0) {
        Future.delayed(const Duration(milliseconds: 500),()=> libre.tcpSocket!.add('DSK:TOT;'.codeUnits));
      } else if (num != 0) {
        if (num != libre.dataTot.value) {
          libre.dataList.clear(); 
          libre.dataSetList.clear();
          libre.dataTot.value = num;
          Future.delayed(const Duration(milliseconds: 500),()=> libre.sendMsgToTcpSocket('DSK:CUR;'));
        }
      }
    }

    if (msg.contains('ELP')) {
      final parts = str.split('/');
      if (parts.length == 2) {
        try {
          libre.playLengths.value = double.parse(parts[0]) / 1000;
          libre.totTalVal.value = double.parse(parts[1]) / 1000;
        } catch (e) {
          // 处理解析异常
          Log.e('Failed to parse play length or total length: $e');
        }
        // 检查定时器是否为空且总长度大于0
        if (libre.timerLoops == null && libre.totTalVal.value > 0) {
          libre.timerLoops = libre.getStartTask();
        }
      }
    }

    if (msg.contains('BTS')) {
      Log.r('code $str');
      final int code = int.tryParse(str) ?? 0;
      if (code > BT_STATUE.values.length - 1 || code < 0) return;
      libre.btsState.value = BT_STATUE.values[code];
      Log.w(libre.btsState.value);
    }

    if (msg.contains('DSK:CUR:')) {
      String att = str.substring(4, str.length);
      libre.usbPlayIndex.value = int.parse(att);
      libre.sendMsgToTcpSocket('DSK:LST:${libre.usbPlayIndex.value - 1}:${libre.usbPlayIndex.value - 1}&');
      Future.delayed(const Duration(milliseconds: 0), ()=> libre.sendMsgToTcpSocket('ELP;'));
      Future.delayed(const Duration(milliseconds: 300), ()=> libre.sendMsgToTcpSocket('PLA;'));
    }

    if (msg.contains('PLA')) {
      if (str == '0') {
        libre.isPlaying.value = false;
        if (libre.timerLoops != null) {
          libre.timerLoops!.cancel();
          libre.timerLoops = null;
        }
      } else if (str == '1') {
        libre.isPlaying.value = true;
        libre.timerLoops ??= libre.getStartTask();
      }

      Future.delayed(const Duration(milliseconds: 300), ()=> libre.sendMsgToTcpSocket('ELP;'));
    }

    if (msg.contains('DSK:LST')) {
      final att = str.substring(4, str.length);
      final id = att.substring(0,att.indexOf(':'));
      String songName = att.substring(att.indexOf(':') + 1, att.length);

      if (songName.contains(';')) {
        String nextMsg = songName.substring(songName.indexOf(';') + 1, songName.length);
        songName = songName.substring(0, songName.indexOf(';'));
        getMsg(nextMsg,libre,buff);
      }

      libre.usbNowSongItem.value = songName;

      for (var el in libre.dataList) {
        if (el['id'].toString() == id) el['name'].value = songName;
      }
    }

    if (msg.contains('LST:') && !(msg.contains('DSK:LST'))) {
      libre.sourceInputList.clear();
      List lt = str.split(',');
      List<String> order = ['NET','BT','LINE-IN','LINE-IN2','PHONO','OPT','HDMI','USB','USBPLAY','USBDAC','COAX'];
      lt.sort((a, b) {
        int indexA = order.indexOf(a);
        int indexB = order.indexOf(b);
        if (indexA == -1 && indexB == -1) {
          return 0;
        } else if (indexA == -1) {
          return 1;
        } else if (indexB == -1) {
          return -1;
        } else {
          return indexA.compareTo(indexB);
        }
      });
      for (var el in lt) {
        Comm.sendSource(libre,el);
      }
    }

    if(msg.contains('TRE:')){
      try{
        libre.treble.value = double.parse(str);
      }catch(_){}
    }

    if(msg.contains('MID:')){
      try{
        libre.mid.value = double.parse(str);
      }catch(_){}
    }
  }

  static getLuciMsg(List<int> msg,Libre libre) async {
    final Map<String,String> mapMsg = ApiManage.luciApi.parseMessage(msg,libre);
    String data = mapMsg['messageData']!;
    if (mapMsg['command'] != '49') Log.h('${libre.name.value}: $mapMsg');
    if (mapMsg['command'] == '64') {
      libre.volume.value = double.parse(data);
    }
    if (mapMsg['command'] == '51') {
      final sourceInput = libre.sourceInput.value;
      if (sourceInput == 'LINE-IN' || sourceInput == 'USBPLAY') return;
      switch (data) {
        case '0':
          // playing
          libre.isPlaying.value = true;
        break;
        case '1':
          // Stopped
          //创建状态机
          libre.createStateMachine();
          libre.stateStep((){
            libre.isPlaying.value = false;
          });
        break;
        case '2':
          // Paused
          libre.isPlaying.value = false;
        break;
        case '3':
          // Connecting
        break;
        case '4':
          // Receiving
        break;
        case '5':
          // Buffering
        break;
      }
    }
    
    if (mapMsg['command'] == '54') {
      if (data == 'error_nonextsong') {
        AppToast.show("No next song");
      } else if (data == 'error_noprevsong') {
        AppToast.show("No prev song");
      }
      
    }

    if (mapMsg['command'] == '561') {
      if (data == '0') {
        libre.inAppleHome.value = false;
      } else if (data == '1') {
        libre.inAppleHome.value = true;
      }
    }

    if (mapMsg['command'] == '114') {
      if (libre.updateState.value == 0 || libre.updateState.value == 99) return;

      libre.updateState.value = 4;
      libre.resetState.value = true;
      
      await Future.delayed(const Duration(milliseconds: 100));

      if (homCon.resetTimer != null) return;

      if (!homCon.upDeviceList.contains(libre.name.value)) {
        final String deviceName = libre.name.value.replaceAll('-BLE', '');
        homCon.upDeviceList.add(deviceName);
        homCon.libreUpList.add(deviceName);
        await Future.delayed(const Duration(milliseconds: 100));
        int num = 0;
        
        homCon.resetTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
          Log.d('正在查找 $num');
          try {
            ApiManage.luciApi.startScan((List<String> result) => homCon.watchForLibre(result));
          } catch(_) {}

          num++;
          if (num >= 20) {
            if (Get.currentRoute == Routes.libreUpdateVersionPage) {
              Get.back();
              AppToast.show("Device discovery timeout. 1");
            }
            homCon.resetTimer?.cancel();
            homCon.resetTimer = null;
          }
        });
      }
    }

    if (mapMsg['command'] == '66') {

      if (libre.updateState.value == 0) {
        libre.updateState.value = 3;
      }
      if (data.length <= 3) {
        try {
          double num = double.parse(data);
          libre.donwloadVerVal.value = num;
        } catch(e) {
          Log.e(e);
        }
      }

      if (data.contains('complete')) {
        if (libre.updateState.value  != 4) libre.updateState.value = 4;
        libre.resetState.value = true;
        libre.needGoToGast.value = true;
        
        if (homCon.resetTimer != null) return;
        if (!homCon.upDeviceList.contains(libre.name.value)) {
          final String deviceName = libre.name.value.replaceAll('-BLE', '');
          homCon.upDeviceList.add(deviceName);
          homCon.libreUpList.add(deviceName);
          await Future.delayed(const Duration(milliseconds: 100));
          int num = 0;
          homCon.resetTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
            Log.d('正在查找 $num');
            try {
              ApiManage.luciApi.startScan((List<String> result) => homCon.watchForLibre(result));
            } catch(_) {}

            num++;
            if (num >= 20) {
              if (Get.currentRoute == Routes.libreUpdateVersionPage) {
                Get.back();
                AppToast.show("Device discovery timeout. 2");
              }
              homCon.resetTimer?.cancel();
              homCon.resetTimer = null;
            }   
          });
        }
      }
    }
    
    if (mapMsg['command'] == '223') {

      if (data == 'NO_UPDATE') {
        // 
        Log.d('homCon.libreSetUpName 名字 ${homCon.libreSetUpName?.name.value}');
        homCon.libreSetUpName?.wifiSetupIndex.value = 7;
        homCon.libreSetUpName = null;
        if (libre.checkOneSec) {
          libre.checkOneSec = false;
        }
      }

      if (homCon.libreSetUpName != null && homCon.libreSetUpName!.name.value == libre.name.value) {
        if (data.length <= 3) {
          try {
            if (libre.updateState.value == 0) {
              libre.updateState.value = 2;
            }
            double num = double.parse(data);
            libre.donwloadVerVal.value = num;
            return;
          } catch(e) {
            Log.e(e);
          }
        }
       
        if (data == 'UPDATE_STARTED') {
          homCon.selectDevice = libre;
          libre.updateState.value = 1;
          Get.back();
          Get.toNamed(Routes.libreUpdateVersionPage);
        }

        if (data == 'UPDATE_DOWNLOAD') {
          libre.updateState.value = 2;
        }
        
        if (data == 'UPDATE_IMAGE_AVAILABLE') {
          libre.donwloadVerVal.value = 0;
          libre.updateState.value = 3;
        }
      } else {
        if (data.length <= 3) {
          try {
            if (libre.updateState.value == 0) {
              libre.updateState.value = 2;
            }
            double num = double.parse(data);
            libre.donwloadVerVal.value = num;
            return;
          } catch(e) {
            Log.e(e);
          }
        }
        if (data == 'NO_UPDATE') {
          libre.updateState.value = 99;
          return;
        }
        if (data == 'UPDATE_STARTED') {
          libre.updateState.value = 1;
          return;
        }
        if (data == 'UPDATE_DOWNLOAD') {
          libre.updateState.value = 2;
          return;
        }

        if (data == 'UPDATE IMAGE AVAILABLE') {
          libre.donwloadVerVal.value = 0;
          libre.updateState.value = 3;
          Get.appUpdate();
          return;
        }
        
        if (data == 'DOWNLOAD_FAIL' || data == 'CRC CHECK ERROR') {
          libre.updateState.value = 5;
        }
        // 更新失败
        
      }
    }

    if (mapMsg['command'] == '38') {
      Log.d(data);
      if (data == '0') {
        Log.d('已断开USB设备');
        libre.usbSongList.clear();
      } else if (data == '1') {
        Log.d('已连接USB设备');
        libre.getSongListUSBforLUCI();
      }
    }

    if (mapMsg['command'] == '42') {
      if (data == '') return;

      final viewKey = 'Window CONTENTS';
      try {
        final resData = json.decode(data);
        final int viewMode = resData['CMD ID'];

        if (!resData.containsKey(viewKey)) return Log.d('command = 42 数据不包含 $viewKey');
        final Map<String,dynamic> viewData = resData[viewKey];
        
        if (viewMode == 1) {
          Log.r('--- 浏览视图 BrowseView模式');
          Log.r(viewData);
          libre.setBrowseView(viewData);
          return;
        }

        if (viewMode == 3) {
          Log.r('--- 播放信息 PlayView模式');
          Log.r(viewData);
          libre.setPlayView(viewData);
          return;
        }
        
      } catch(e) {
        Log.e(e);
      }
    }

      // try {
      //   if (data == '') return;
      //   String newdata = data;
      //   newdata = newdata.substring(0,newdata.lastIndexOf('}') + 1);
      //   Map res = json.decode(newdata);
      //   final Map item = res['Window CONTENTS'];
      //   final browser = item['Browser'];

      //   if (browser == 'HOME') {
      //     final itemCount = item['Item Count'];
      //     if (itemCount <= 0) return;

      //     final itemList = item['ItemList'];
      //     Log.r(itemList);
      //     for (var sed in itemList) {
      //       if (sed['Name'] == 'USB') {
      //         int fileId = sed['Item ID'];
      //         libre.selectItem(fileId);
      //       }
      //     }
      //     return;
      //   }

      //   if (browser == 'USB') {
      //     int pageIndex = item['PageNo'];
      //     libre.usbPageIndex.value = pageIndex;
      //     Log.d('当前在第 $pageIndex 页');

      //     final itemCount = item['Item Count'];
      //     if (itemCount <= 0) return;

      //     if (pageIndex == 1) {
      //       int totalItems = item['TotalItems'];
      //       libre.usbTotalSongs.value = totalItems;
      //       if (totalItems == 0) return Log.d('当前没有歌曲');

      //       int allPages = (totalItems / 20).ceil();
      //       libre.usbPages = allPages;
      //       Log.d('当前USB总共有 $totalItems 首歌, 总共有 $allPages 页');
           
      //     }

      //     if (libre.usbSongList.length == libre.usbTotalSongs.value) return;

      //     List itemList = item['ItemList'];
      //     for (var el in itemList) {
      //       final Map<String,String> map = {
      //         'name': '${el['Name']}',
      //         'id': '${el['Item ID']}',
      //         'page': '$pageIndex',
      //       };
      //       if (libre.usbSongList.contains(map)) continue;
      //       libre.usbSongList.add(map);
      //     }
          

      //     if (libre.usbPageIndex.value < libre.usbPages) {
      //       await Future.delayed(const Duration(milliseconds: 300));
      //       libre.downItem();
      //     }
      //     Log.d(libre.usbSongList);
      //   }

      //   if (res['Title'] == 'PlayView') {
      //     Log.r(res);
      //     //处理playUIView闭包
      //     refreshPlayUI(){
      //       if (libre.sourceInput.value != 'LINE-IN' && libre.sourceInput.value != 'BT') {
      //       if (item['PlayState'] == 0) {
      //         libre.isPlaying.value = true;
      //       } else {
      //         libre.isPlaying.value = false;
      //       }
      //     }
          
      //     libre.seek.value = item['Seek'] ?? false;
      //     libre.nextBtn.value = item['Next'] ?? false;
      //     libre.prevBtn.value = item['Prev'] ?? false;
          
      //     // Log.d('TRACK: ${item['TrackName'] != 'Not provided' && item['TrackName'] != ''}');
      //     if (item['TrackName'] != 'Not provided' && item['TrackName'] != '') {
      //       libre.songName.value = item['TrackName'];
      //       libre.songArtist.value = item['Artist'];
      //       libre.songAlbum.value = item['Album'];
      //       libre.sourceCurrent.value = item['Current Source'].toString();
      //       if (!item.containsKey('CoverArtUrl')) {
      //         libre.songImage.value = '';
      //       } else {
      //         String imgUrl = item['CoverArtUrl'];
      //         if (imgUrl == '') {
      //           libre.songImage.value = '';
      //         } else {
      //           if (!imgUrl.contains('http')) {
      //             if (libre.sourceCurrent.value == '22' && item['CoverArtUrl'] == 'defaultArt.jpg') {
      //               libre.songImage.value = '';
      //             }
      //             if (item['CoverArtUrl'] == 'coverart.jpg') {
      //               final url = 'http://${libre.ip}/${item['CoverArtUrl']}?Album=${libre.songAlbum.replaceAll(RegExp(r'\s+'), '')}';
      //               libre.setSongImage(url);
      //             }
      //           } else {
      //             libre.setSongImage(item['CoverArtUrl']);
      //           }
      //         }
              
      //       }
      //     }

      //     if (item['PlayUrl'] != null) {
      //       String playUrl = item['PlayUrl'];
      //       if (playUrl.contains('spotify:episode')) {
      //         Log.d('开始播放 POD CAST');
      //         libre.spotifyPodCast.value = true;
      //       } else {
      //         libre.spotifyPodCast.value = false;
      //       }
      //     } else {
      //       libre.spotifyPodCast.value = false;
      //     }

      //     if (item['Mime'] != null) libre.mime.value = item['Mime'];
      //     if (item['SampleRate'] != null) libre.sampleRate.value = item['SampleRate'];

      //     try {
      //       if (double.parse(item['TotalTime'].toString()) > 0) {
      //         libre.totTalVal.value = double.parse(item['TotalTime'].toString()) / 1000;
      //       }
      //     } catch (e){
      //       Log.e('?????? $e');
      //     }

      //     if (item['TrackName'] == '') {
      //       if (item['TotalTime'] > 0) return;
      //       if (item['Album'] != '') return;
      //       if (item['Artist'] != '') return;
      //       if (item['CoverArtUrl'] != '') return;
      //       libre.sourceCurrent.value = '0';
      //       libre.songName.value = '';
      //       libre.songArtist.value = '';
      //       libre.songAlbum.value = '';
      //       libre.songImage.value = '';
      //       libre.totTalVal.value = 0.1;
      //       libre.playLengths.value = 0.0;
      //       libre.mime.value = '';
      //       libre.sampleRate.value = '';
      //     }
      //     }

      //     处理状态机
      //     if(item.containsKey('TrackName')&&item['TrackName'].toString().isNotEmpty){
      //         libre.stateValid();
      //         refreshPlayUI();
      //     }else{
      //         libre.stateStep((){
      //           refreshPlayUI();
      //         });
      //     }
          
      //   }
      // } catch (e) {
      //   Log.e(data);
      //   Log.e(e);
      // }
    

    if (mapMsg['command'] == '49') {
      try {
        libre.playLengths.value =  (double.parse(data) / 1000);
      } catch (_){}
    }

    if (mapMsg['command'] == '50') {
      try {
        libre.sourceCurrent.value = data;
      }catch(_){}
    }

    // if (mapMsg['command'] == '243') {
    //   Log.r('重启data == $data');
    //   if (data == 'ROON_STARTED') {
    //     Log.y('${libre.name.value} 初始化完成');
    //     if (homCon.libreUpList.contains(libre.name.value)) {
    //       homCon.libreUpList.remove(libre.name.value);
    //       await Future.delayed(const Duration(seconds: 1));
    //       Log.y('${libre.name.value} 需要 UPdateVer');
    //       Log.y('先发送一条消息看看设备连接是否成功');
    //       libre.getVolume();
    //       await Future.delayed(const Duration(milliseconds: 300));
    //       libre.getMute();
    //       Log.w('最后发送UPdatever');
    //       await Future.delayed(const Duration(seconds: 1));
    //       libre.getUpdateVer();
    //     }
    //   }
    // }

    if (mapMsg['command'] == '572') {
      if (data == '') return;
      Map res = json.decode(data);
      // Log.d(res);
      if (res['id'] == 'status') {
        if (res['tos'] == 'not_activated') {
          libre.pairGoToGcast.value = true;

          if (!homCon.needSetGCast.value) {
            homCon.needSetGCast.value = true;
          }
          if (homCon.libreSetUpName != null) {  
            if (libre.name.value == homCon.libreSetUpName!.name.value) {
              Log.e('${libre.name.value} need set GCast');
              homCon.selectDevice = libre;
            }
          }
          libre.tos.value = false;
        } else {
          libre.tos.value = true;
        }
        
        if (res['crash_report'] != null && res['crash_report']) {
          libre.crashReport.value = true;
        } else {
          libre.crashReport.value = false;
        }
      }

      if (res['id'] == '') {
        String gmsg = res['status_msg'];
        
        if (homCon.showTos.value == false) {
          homCon.showTos.value = true;
          homCon.showTosContext.value = gmsg;
        }

        await Future.delayed(const Duration(seconds: 2), () {
          homCon.showTos.value = false;
          homCon.showTosContext.value = '';
        });
      }

      if (res['id'] == 'tos') {
        String gmsg = res['status_msg'];
        AppToast.show(gmsg);
        if (gmsg.contains('successfully')) {
          Get.toNamed('/libreSetInfo/setGcast2');
          libre.tos.value = true;
        }
      }

      if (res['id'] == 'crash_report') {
        if (res['action'] == 'accepted') {
          libre.crashReport.value = true;
        } else {
          libre.crashReport.value = false;
        }
      }
    }

    //
    if (mapMsg['command'] == '573') {
      Log.d(data);
      libre.timeZone.value = data;
    }

    if (mapMsg['command'] == '63') {
      if (data == 'MUTE') {
        libre.mute.value = true;
        if (libre.sourceInput.value == 'LINE-IN') libre.isPlaying.value = false;
      } else if (data == 'UNMUTE') {
        libre.mute.value = false;
        if (libre.sourceInput.value == 'LINE-IN') libre.isPlaying.value = true;
      }
      libre.getVolume();
    }

    if (mapMsg['command'] == '91') {
      if (libre.netInfo.value != '') return;
      if (data.contains('Wlan0')) {
        libre.netInfo.value = data.replaceAll(RegExp(r'Wlan0'), 'WLAN');
      } else if (data.contains('Eth0')) {
        libre.netInfo.value = data.replaceAll(RegExp(r'Eth0'), 'ETH');
      }
    }

  }
}