import 'dart:async';
import 'dart:io';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:gocontrol/models/class/class_ble.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/models/model_class/a4_usblist.dart';
import 'package:gocontrol/models/model_class/a5_playinfo.dart';

/// 网络设备类
abstract class AbstractNET implements 
  DeviceBase,
  AbstractBLE,
  A4Usblist,
  A5Playinfo {

  // 1.基础信息
  int get serverPort; // 服务端口
  String get httpStr; // http协议开头 A31是http A97是https
  String get ip; // 网络ip
  String get uuid; // uuid
  String get project; // project
  String get mac; // mac地址
  String get eth2; // staMac地址
  String get mcuVersion; // mcu版本
  String get language; // 提示音语言
  RxInt get plmSource; // 备用输入源
  RxList<Map> get plmSourceInputList; // 备用输入源列表
  void plmGetSource(); // 解析备用输入源

  // 2.连接
  Socket? get socket; // Socket
  RxBool get socketState; // sokcet的连接状态
  RxString get lastSendMsg; // 上一次发送的消息
  Future<void> connectSocket(); // 连接socket
  Future<void> reconnectSocket(); // 重连socket
  Future<void> closeSocket();
  void sendMsgToSocket(String msg); // 给Socket发送消息

  // 3.配网
  // RxBool get setupMode; // 当前配网模式
  // RxInt get beginWiFiSetup; // 当前配网步骤
  // RxInt get setupWiFiMsgLen; // 当前配网的信息的长度，用来拼接的数据的时候对接长度
  // RxList<int> get setupWiFiMsg; // 当前配网信息的数据内容
  // RxList get wifiScanList; // 当前配网缓环节搜索到的wifi
  // RxMap get selectWiFiInfo; // 当前配网过程选择的wifi
  // RxBool get connectWiFiState; // 当前是否正在连接wifi
  // RxInt get connectWiFiResult; // WIFI连接结果如何
  // RxString get typeModel;
  // void sendSetup1001(); // 发送配网1001信息
  // void sendSetup1002(); // 发送配网1002信息
  // Future<void> sendSetup1003(String msg); // 发送配网1003信息

  // 4.播放状态控制
  RxBool get rakoitAPI; // 是否可以使用RAKOIT的API
  RxBool get isPlaying; // 播放状态
  RxString get playMedium; // 设备播放的模式
  RxBool get playLoading;
  void getPlaying(); // 获取播放状态
  void initNetInfo({bool reconnect}); // 初始化网络设备信息

  // 5.网络请求部分
  Future<String> request(String requestUrl); // 发送请求
  Future<Map> requestPost({required String url,required String hsa,required String dat}); // 发送post

  // 7.播放列表
  RxList<Map> get songList; // 当前歌曲列表
  RxString get songlistName; // 当前歌曲列表的名字
  void getPlaySongList(); // 获取播放歌曲列表

  // 8.多房间
  RxBool get isRoomChild; // 是否被其他设备组成多房间
  RxList<String> get roomChildren; // Master时多房间的子设备列表
  RxBool get showAllVolume; // Master是否显示多房间的列表音量
  RxDouble get allVolume; // Master多房间的总音量
  RxDouble get lastAllVolume; // Master上一次的总音量
  void getMatchingVolumes(); // 计算多房间总音量
  void setAllVolume(double ov); // 设置多房间总音量
  Future<void> getSlaveList(); // 获取设备SlaveList多房间信息
  Future<void> joinGroupMaster(String ip); // 作为child加入多房间列表
  Future<void> slaveKickout(String ip); // Master将某个child踢出多房间列表
  
  // 9.radio
  void playQueue(String queueName);
  void setSongIndex(String name,int index);

  // loop
  RxBool get loopRandom;
  RxInt get loopMode;
  RxBool get keySound;

  RxDouble get fmVal;
  CarouselSliderController get fmCon;
  RxString get lastMhz;
  RxList get fmPresetList;
  void changeFMPage();
  void setFMPage();
  
  RxDouble get lastVolume;

  // ble
  BluetoothCharacteristic? get notify2;
  BluetoothCharacteristic? get write2;
  void sendMsgToBLE2({String? msg, List<int>? byteMsg});
}

