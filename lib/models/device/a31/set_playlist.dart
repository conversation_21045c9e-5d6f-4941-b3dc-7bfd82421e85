
import 'package:get/get.dart';
import 'package:gocontrol/common/comm.dart';
import 'package:gocontrol/common/net_api.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';

mixin SetDevicePlayList on AbstractNET {
  @override // 当前歌曲列表的名字
  RxString songlistName = ''.obs;
  @override // 当前歌曲列表
  RxList<Map> songList = RxList<Map>([]);

  @override // 获取播放歌曲列表
  void getPlaySongList({String name = ''}){
    try{
      requestPost(
        url: WBrowseQueue.url(ip),
        hsa: WBrowseQueue.hsa,
        dat: WBrowseQueue.dat(queueName: name)
      ).then((body){
        String res = body['u:BrowseQueueResponse']['QueueContext'];
        songlistName.value = Comm.translateInfo(res)['PlayList']['ListName'];
        if (res.contains('<?xml')) {
          Map tracks = Comm.translateInfo(res)['PlayList']['Tracks'];
          List<Map> trackList = tracks.entries.map((entry) => {entry.key: entry.value}).toList();
          if (trackList.length != songList.length) {
            songList.clear();
            for (var el in trackList) {
              songList.add(el);
            }
          }
        } else {
          Log.r('另一种播放歌曲列表 $res');
        }
      });
    }catch(_){
      Log.e('获取播放列表失败了');
    }
  }
}