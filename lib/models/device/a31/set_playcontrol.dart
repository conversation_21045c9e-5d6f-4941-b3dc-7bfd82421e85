
import 'dart:async';

import 'package:get/get.dart';
import 'package:gocontrol/common/net_api.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/a31/a31.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';

mixin SetDevicePlayControl on AbstractNET {
  // 播放状态控制--------------[start]---------------
  // 是否可以使用RAKOIT的API
  @override
  RxBool rakoitAPI = false.obs;
  // 播放状态
  @override
  RxBool isPlaying = false.obs;
  // 设备播放的模式
  @override  
  RxString playMedium = ''.obs;

  @override // 获取播放状态
  void getPlaying()=> sendMsgToSocket('MCU+SNG+GET');

  @override // 初始化网络设备信息
  void initNetInfo({bool reconnect = false}) async {
    final device = this as A31;
    try {
      if (device.deviceMode.value != 'H50' || device.deviceMode.value != 'A98') await getSlaveList();
    } catch (e) {
      Log.e('初始化网络设备信息失败: ${device.deviceMode.value}');
    }
    await getPlayInfo();
    await Future.delayed(const Duration(milliseconds: 0), ()=> getVolume());
    await Future.delayed(const Duration(milliseconds: 200), ()=> getVersion());
    await Future.delayed(const Duration(milliseconds: 200), (){
      getSourceInput();
      Future.delayed(const Duration(seconds: 1), () {
        if (sourceInput.value == '') getSourceInput();
      });
    });
    if (!reconnect) {
      await Future.delayed(const Duration(milliseconds: 200), (){
        getSourceList();
        Future.delayed(const Duration(seconds: 1), () {
          if (sourceInputList.isEmpty) getSourceList();
        });
      });
      homCon.addToDeviceList(this);
    }
  }
  // 播放状态控制--------------[end]---------------
  

  // 播放控制--------------[start]---------------
  @override // 上一曲
  void prevPlay() {
    if (origin == Origins.ble) return sendMsgToBLE(msg: 'PRE;');
    if (rakoitAPI.value) {
      if (project.contains('A100')) {
        sendMsgToSocket('MCU+PAS+RAKOIT:FWD:PRE&');
      } else {
        sendMsgToSocket('MCU+PAS+RAKOIT:PRE&');
      }
    } else {
      requestPost(
        url: WPrevious.url(ip),
        hsa: WPrevious.hsa,
        dat: WPrevious.dat
      );
    }
  }

  @override // 下一曲
  void nextPlay() {
    if(origin == Origins.ble) return sendMsgToBLE(msg: 'NXT;');
    if(rakoitAPI.value){
      if (project.contains('A100')) {
        sendMsgToSocket('MCU+PAS+RAKOIT:FWD:NXT&');
      } else {
        sendMsgToSocket('MCU+PAS+RAKOIT:NXT&');
      }
    }else{
      requestPost(
        url: WNext.url(ip),
        hsa: WNext.hsa,
        dat: WNext.dat
      );
    }
  }

  @override // 播放
  void playSong() {
    if(origin == Origins.ble) return sendMsgToBLE(msg: 'POP;');
    if(rakoitAPI.value){
      if (project.contains('A100')) {
        sendMsgToSocket('MCU+PAS+RAKOIT:FWD:POP&');
      } else {
        sendMsgToSocket('MCU+PAS+RAKOIT:POP&');
      }
    }else{
      sendMsgToSocket('MCU+PLY+PUS');
    }
    Future.delayed(const Duration(milliseconds: 100),()=> getPlayInfo());
  }

  @override // 暂停
  void pauseSong() {}

  @override // 播放和暂停
  void playAndPauseSong() {}
  // 播放控制--------------[end]---------------
}