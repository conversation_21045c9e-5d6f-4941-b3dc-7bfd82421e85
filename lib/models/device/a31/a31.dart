import 'dart:convert';
import 'dart:async';
import 'package:carousel_slider/carousel_controller.dart';
import 'package:get/get.dart';
import 'package:gocontrol/api/api.dart';
import 'package:gocontrol/common/comm.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/common/toast.dart';
import 'package:gocontrol/log.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:gocontrol/common/ble_msg.dart';
import 'package:gocontrol/common/net_api.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/device/a31/set_playinfo.dart';
import 'package:gocontrol/models/model_class/set_network.dart';
import 'package:gocontrol/models/device/a31/set_playcontrol.dart';
import 'package:gocontrol/models/device/a31/set_playlist.dart';
import 'package:gocontrol/models/device/a31/set_request.dart';
import 'package:gocontrol/models/device/a31/set_slave.dart';
import 'package:gocontrol/models/device/a31/set_sokcet.dart';
import 'package:gocontrol/models/device/a31/set_source.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/models/model_class/a3_services.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/routes/routes.dart';

class A31 extends 
  AbstractNET with 
  SetDeviceNetWork, // 配网
  SetDeviceSocket, // sokcet
  SetDeviceSource, // 输入源
  SetDeviceRequest, // 请求
  SetDevicePlayControl, // 播放控制
  SetDevicePlayInfo, // 设备播放信息
  SetDevicePlayList, // 播放列表
  SetDeviceSlave, // 多房间
  BLEandOnServices,
  BaseAudio {

  A31();

  // 创建一个网络设备
  A31.createToNetwork(this.ip){
    origin = Origins.net;
  }

  A31.createToBLE({
    required nam,
    required scanResult
  }){
    origin = Origins.ble;
    name.value = nam;
    device = scanResult;
  }

  // 基础信息 base --------------[start]---------------
  @override // 设备发现的类型
  late Origins origin;
  @override // 设备名字
  RxString name = ''.obs;
  @override
  RxString version = ''.obs;
  @override
  RxBool led = false.obs;
  @override
  RxInt vst = 3.obs;
  @override
  RxBool updateVer = false.obs;
  @override
  RxString updateStr = ''.obs;
  @override
  RxBool keySound = false.obs;
  @override
  RxDouble fmVal = 87.0.obs;
  @override
  CarouselSliderController fmCon = CarouselSliderController();
  @override
  RxString lastMhz = ''.obs;
  @override
  RxList fmPresetList = RxList(['0','0','0','0','0','0']);
  @override
  RxDouble lastVolume = 0.0.obs;

  StreamSubscription? notifyListen;
  StreamSubscription? notifyListen2;
  StreamSubscription? bleStateListen;

  @override
  void changeFMPage({bool jump = false}){
    try {
      int page = ((fmVal.value * 10) - (87 * 10)).toInt();
      if (Get.currentRoute == Routes.fmPage) {
        if (jump) {
          fmCon.jumpToPage(page);
          return;
        }
        fmCon.animateToPage(page);
      }
    } catch(_) {}
  }
  
  @override
  void setFMPage() {
    String mhz = (fmVal.value * 1000).toStringAsFixed(0);
    lastMhz.value = mhz;
    sendMsgToSocket('MCU+PAS+RAKOIT:FWD:FMQ:$mhz&');
  }
  
  @override
  void updateConfirm(){
    // sendMsgToSocket('UPG:CONFIRM;');
  }

  @override
  void vstAdd(){
    sendMsgToSocket('MCU+PAS+RAKOIT:VST:${vst.value + 1}&');
  }
  
  @override
  void vstSub(){
    sendMsgToSocket('MCU+PAS+RAKOIT:VST:${vst.value - 1}&');
  }

  @override // 获取当前设备的名字
  void getDeviceName() async {
    sendMsgToSocket('MCU+PAS+RAKOIT:NAM&');
    await Future.delayed(const Duration(seconds: 2), (){
      if (name.value == '') sendMsgToSocket('MCU+PAS+RAKOIT:NAM&');
    });
    await Future.delayed(const Duration(seconds: 2), (){
      if (name.value == '') {
        homCon.adevices.remove(this);
      }
    });
  }

  @override // 设置当前设备名字
  void setDeviceName(String nm) {
    sendMsgToSocket('MCU+PAS+RAKOIT:$nm&');
    String st = nm.substring(4, nm.length);
    name.value = Comm.hexStrToString(st);
    Future.delayed(const Duration(seconds: 1),(){
      sendMsgToSocket('MCU+PAS+RAKOIT:NAM&');
    });
  } 

  @override // 获取设备MCU版本,利用这个检测是否支持RAKOIT API;
  void getVersion() => sendMsgToSocket('MCU+PAS+RAKOIT:VER&');

  @override
  void getUpdateVer() {
    sendMsgToSocket('MCU+PAS+RAKOIT:UPG&');
  }

  // 基础信息 net --------------[start]---------------
  @override // 服务端口
  int serverPort = 8899;

  @override // http协议开头 A31是http A97是https
  String httpStr = 'http';

  @override // 网络ip
  String ip = '';

  @override // 设备uuid
  String uuid = '';

  @override // 设备project
  String project = '';

  @override // mac地址
  String mac = '';

  @override // eth2地址
  String eth2 = '';

  @override // mcu版本
  String mcuVersion = '';
  
  @override
  String language = '';
  
  RxString deviceMode = 'A31'.obs;
  
  @override
  void resetDevice(){
    sendMsgToSocket('MCU+PAS+RAKOIT:SYS:RESET&');
  }
  // 基础信息--------------[end]---------------


  // 音量--------------[start]---------------
  @override 
  RxDouble volume = 0.0.obs; 
  
  @override 
  void getVolume() => sendMsgToSocket(ApiManage.tcpApi.volumeGet);
  
  @override 
  void setVolume(double vol) {
    int nvol = vol.round();
    String formattedVol = '0';
    switch (nvol) {
      case 100:
        formattedVol = '100';
        break;
      case 0:
        formattedVol = '000';
        break;
      default:
        if (nvol < 10) {
          formattedVol = '00$nvol';
          break;
        } else {
          formattedVol = '0$nvol';
          break;
        }
    }
    if (origin == Origins.ble) {
      sendMsgToBLE(msg: 'VOL:$formattedVol;');
    } else {
      sendMsgToSocket('MCU+VOL+$formattedVol');
      Future.delayed(const Duration(milliseconds: 100), ()=> getVolume());
    }
  }

  
  // 音量--------------[end]---------------


  // 电台Radio--------------[start]---------------
  @override
  void playQueue(String queueName) {
    requestPost(
      url: PlayQueue.url(ip),
      hsa: PlayQueue.hsa,
      dat: PlayQueue.dat(queueName)
    ).then((value) {
      Log.w(value);
    });
  }

  @override
  void setSongIndex(String name,int index){
    try{
      requestPost(
        url: WPlayQueueWithIndex.url(ip),
        hsa: WPlayQueueWithIndex.hsa,
        dat: WPlayQueueWithIndex.dat(name,index) 
      );
    }catch(_){}
  }
  // 电台Radio--------------[end]---------------

  
  // BLE部分--------------[start]---------------

  void setupNetWork(){
    sendMsgToSocket('MCU+PAS+RAKOIT:WRS&');
  }

  @override // BLE设备的实例
  BluetoothDevice device = BluetoothDevice(remoteId: const DeviceIdentifier(''));
  @override // BLE的notfiy通知特征值
  BluetoothCharacteristic? notify;
  @override // BLE的写通知特征值
  BluetoothCharacteristic? write;
  @override // BLE的连接状态
  RxBool isBLEConnected = false.obs;
  // 网使用的特征值
  @override
  BluetoothCharacteristic? notify2;
  @override
  BluetoothCharacteristic? write2;
  @override
  RxBool reconnecting = false.obs;

 @override
  Future<void> connectBLE() async {
    try{
      await device.connect(timeout: const Duration(seconds: 10));
      if (device.isConnected) {
        await onServices2();
        if (write2 != null) listenBLEStateChange();
      } else {
        // 连接失败需要移除此设备
        Log.e('${device.platformName} xxx连接失败');
        Get.back();
      }
    } catch(err) {
      homCon.adevices.remove(this);
      Log.e('$err xxx连接失败');
    }
  }

  Future<void> onServices2() async {
    try{
      List<BluetoothService> services = await device.discoverServices();
      for (var service in services) {
        // 01000100-4c69-6e6b-506c-************
        if(service.uuid.toString() == '01000100-4c69-6e6b-506c-************'){
          Log.d('配网开启特征服务...');
          var characteristics = service.characteristics;
          for (BluetoothCharacteristic c in characteristics) {
            if (c.properties.write || c.properties.writeWithoutResponse) write2 = c;
            if (c.properties.notify ) {
              notifyListen2 = c.onValueReceived.listen((msg)=> BleMsg.listenSetupNetWork(msg,this));
              try{
                await c.setNotifyValue(true);
                notify2 = c;
              }catch(_){}
            }
          }
        }
      }
    }catch(e){
      Log.e(e);
    }
  }

  @override // 关闭BLE
  Future<void> disconnectBLE() async {
    await device.disconnect();
  }

  @override // 重连BLE
  Future<void> reconnectBLE() async {
    
  }

  @override // 监听BLE连接状态
  void listenBLEStateChange() {
    bleStateListen = device.connectionState.listen((event){
      // 设备的连接情况
      if (event == BluetoothConnectionState.connected) {
        Log.d('${device.platformName}连接成功');
        isBLEConnected.value = true;
      } else if (event == BluetoothConnectionState.disconnected) {
        Log.d('${device.platformName}断开连接');
        
        isBLEConnected.value = false;
        // 清除
        if (notifyListen != null) {
          notifyListen!.cancel();
          notifyListen = null;
        }
        if (notifyListen2 != null) {
          notifyListen2!.cancel();
          notifyListen2 = null;
        }
        if (bleStateListen != null) {
          bleStateListen!.cancel();
          bleStateListen = null;
        }
      }
    });
  }

  @override // 给BLE发送消息
  void sendMsgToBLE({String? msg, List<int>? byteMsg}){
    try{
      // if(!isBLEConnected.value || write == null) return Log.d('[${device.platformName}]打算发送[$msg],但是设备已经断开连接或无写入特征值');
      // if(msg != null) write!.write(ascii.encode(msg),withoutResponse: write!.properties.writeWithoutResponse);
      // if(byteMsg != null) write!.write(byteMsg,withoutResponse: write!.properties.writeWithoutResponse);
      
      if(!device.isConnected) return Log.d('[${device.platformName}]打算发送[$msg],但是设备已经断开连接');
      if(write == null) return Log.d('[${device.platformName}]打算发送[$msg],但是设备无写入特征值');
      if(msg != null) write!.write(ascii.encode(msg),withoutResponse: write!.properties.writeWithoutResponse);
      if(byteMsg != null) write!.write(byteMsg,withoutResponse: write!.properties.writeWithoutResponse);
    }catch(_){
      Log.e('[${device.platformName}]写入数据失败');
    }
  }

  @override // 配网BLE消息
  void sendMsgToBLE2({String? msg, List<int>? byteMsg}){
    try{
      // if(!isBLEConnected.value || write2 == null) return Log.d('[${device.platformName}]打算发送[$msg],但是设备已经断开连接或无写入特征值');
      if(!device.isConnected) return Log.d('[${device.platformName}]打算发送[$msg],但是设备已经断开连接');
      if(write2 == null) return Log.d('[${device.platformName}]打算发送[$msg],但是设备无写入特征值');
      if(msg != null) write2!.write(ascii.encode(msg),withoutResponse: write2!.properties.writeWithoutResponse);
      if(byteMsg != null) write2!.write(byteMsg,withoutResponse: write2!.properties.writeWithoutResponse);
    }catch(_){
      Log.e('[${device.platformName}]写入数据失败');
    }
  }

  @override // 初始化BLE信息
  Future<void> initBLEInfo() async {
    await Future.delayed(const Duration(milliseconds: 0),()=> getDeviceName());
    await Future.delayed(const Duration(milliseconds: 200),()=> getVolume());
    await Future.delayed(const Duration(milliseconds: 200),()=> getSourceInput());
    await Future.delayed(const Duration(milliseconds: 200),()=> getSourceList());
  }
  
  // BLE部分--------------[end]---------------
  

  @override
  RxDouble treble = 0.0.obs;
  @override
  RxDouble mid = 0.0.obs;
  @override
  RxDouble bass = 0.0.obs;
  @override
  RxDouble maxVolume = 30.0.obs;
  @override
  RxBool crossover = false.obs;
  @override
  RxDouble balance = 0.0.obs;

  @override
  void initAudioData() async {
    Future.delayed(const Duration(milliseconds: 0),()=> getTreble());
    await Future.delayed(const Duration(milliseconds: 200),()=> getMid());
    Future.delayed(const Duration(milliseconds: 0),()=> getBass());
    await Future.delayed(const Duration(milliseconds: 200),()=> getBalance());
    Future.delayed(const Duration(milliseconds: 0),()=> getMaxVolume());
    await Future.delayed(const Duration(milliseconds: 200),()=> getCrossover());
    Future.delayed(const Duration(milliseconds: 0),()=> getFrequency());
    await Future.delayed(const Duration(milliseconds: 200),()=> getDeepBass());
    Future.delayed(const Duration(milliseconds: 0),()=> getIntensity());
    await Future.delayed(const Duration(milliseconds: 200),()=> getEqIndex());
    Future.delayed(const Duration(milliseconds: 0),()=> getEqList());
    await Future.delayed(const Duration(milliseconds: 200),()=> getCustomEQList());
  }

  @override
  void getTreble() {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'TRE;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:TRE&');
    }
  }
  
  @override
  void setTreble(double val) {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'TRE:${val.ceil()};');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:TRE:${val.ceil()}&');
    }
  }

  @override
  void getMid() {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'MID;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:MID&');
    }
  }

  @override
  void setMid(double val) {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'MID:${val.ceil()};');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:MID:${val.ceil()}&');
    }
  }
  
  @override
  void getBass() {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'BAS;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:BAS&');
    }
  }

  @override
  void setBass(double val) {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'BAS:${val.ceil()};');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:BAS:${val.ceil()}&');
    }
  }

  @override
  void getBalance() {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'BAL;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:BAL&');
    }
  }

  @override
  void setBalance(double val) {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'BAL:${val.ceil()};');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:BAL:${val.ceil()}&');
    }
  }
  
  @override
  void getMaxVolume() {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'MXV;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:MXV&');
    }
  }

  
  @override
  void setMaxVolume(double val) {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'MXV:${val.ceil()};');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:MXV:${val.ceil()}&');
    }
  }

  @override
  void getCrossover() {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'CFE;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:CFE&');
    }
  }

  @override
  void setCrossover(bool val) {
    if(origin == Origins.ble){
      if(val){
        sendMsgToBLE(msg: 'CFE:1;');
      }else{
        sendMsgToBLE(msg: 'CFE:0;');
      }
    }else{
      if(val){
        sendMsgToSocket('MCU+PAS+RAKOIT:CFE:1&');
      }else{
        sendMsgToSocket('MCU+PAS+RAKOIT:CFE:0&');
      }
    }
  }
  

  @override
  RxDouble frequency = 50.0.obs;

  @override
  void getFrequency(){
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'CFF;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:CFF&');
    }
  }

  @override
  void setFrequency(double val){
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'CFF:${val.ceil()};');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:CFF:${val.ceil()}&');
    }
  }

  @override
  RxBool deepBass = false.obs;

  @override
  void getDeepBass(){
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'VBS;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:VBS&');
    }
  }
  
  @override
  void setDeepBass(bool val){
    if(origin == Origins.ble){
      if(val){
        sendMsgToBLE(msg: 'VBS:1;');
      }else{
        sendMsgToBLE(msg: 'VBS:0;');
      }
    }else{
      if(val){
        sendMsgToSocket('MCU+PAS+RAKOIT:VBS:1&');
      }else{
        sendMsgToSocket('MCU+PAS+RAKOIT:VBS:0&');
      }
    }
    
  }

  @override
  RxDouble intensity = 0.0.obs;

  @override
  void getIntensity(){
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'VBI;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:VBI&');
    }
  }

  @override
  void setIntensity(double val){
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'VBI:${val.ceil()};');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:VBI:${val.ceil()}&');
    }
  }
  

  @override
  RxInt eqIndex = 0.obs;

  @override
  RxList<Map> eqList = RxList<Map>([]);

  @override
  void setEqIndex(int index){
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'EQS:$index;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:EQS:$index&');
    }
  }

  @override
  void getEqIndex(){
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'EQS;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:EQS&');
    }
  }

  @override
  void getEqList(){
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'PEQ;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:PEQ&');
    }
  }

  @override
  void setEqList(String str,{bool?custom}){
    List list = str.split(',');
    if(custom == null){
      eqList.clear();
      for(String item in list){
        eqList.add({
          'id': int.parse(item.substring(0,item.indexOf('@'))),
          'name': item.substring(item.indexOf('@') + 1,item.length),
        });
      }
    }
  }

  // loop
  @override
  RxBool loopRandom = false.obs;
  @override
  RxInt loopMode = 0.obs;


  // 自定义eq
  @override
  RxList<Map> customEQList = RxList<Map>([]);

  @override 
  final customEQindex = RxInt(-1);

  @override
  List<Map> customEQValueList = [
    {
      "freq": '125',
      "gain": RxDouble(0)
    },
    {
      "freq": '250',
      "gain": RxDouble(0)
    },
    {
      "freq": '500',
      "gain": RxDouble(0)
    },
    {
      "freq": '1000',
      "gain": RxDouble(0)
    },
    {
      "freq": '2000',
      "gain": RxDouble(0)
    },
    {
      "freq": '4000',
      "gain": RxDouble(0)
    },
    {
      "freq": '8000',
      "gain": RxDouble(0)
    },
    {
      "freq": '16000',
      "gain": RxDouble(0)
    }
  ];

  @override
  void getCustomEQList() {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: 'CEQ:LST;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:CEQ:LST&');
    }
  }

  @override
  void customEQListValReset() {
    for (var el in customEQValueList) {
      el['gain'].value = 0.0;
    }
  }

  @override
  void getCEQandFLT(String val) {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: '$val;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:$val&');
    }
  }
  
  @override
  void initCustomEQval() async {
    for (int i = 0; i < customEQValueList.length; i++) {
      await Future.delayed(const Duration(milliseconds: 100),(){
        if(origin == Origins.ble){
          sendMsgToBLE(msg: 'CEQ:FLT:$i;');
        }else{
          sendMsgToSocket('MCU+PAS+RAKOIT:CEQ:FLT:$i&');
        }
      });
    }
  }

  @override
  void setFLT(String val) {
    if(origin == Origins.ble){
      sendMsgToBLE(msg: '$val;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:$val&');
    }
  }

  @override
  void saveCustomEQ() {
    int id = eqIndex.value - 10;
    String val = 'CEQ:SAV:$id:${customEQList[id]['name']}'; 
    if(origin == Origins.ble){
      sendMsgToBLE(msg: '$val;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:$val&');
    }
  }

  @override
  void delectCustomEQ() {
    int id = eqIndex.value - 10;
    String val = 'CEQ:DEL:$id';
    setEqIndex(0);
    if(origin == Origins.ble){
      sendMsgToBLE(msg: '$val;');
    }else{
      sendMsgToSocket('MCU+PAS+RAKOIT:$val&');
    }
    Future.delayed(const Duration(milliseconds: 180),()=> getCustomEQList());
  }

  @override
  void clearCustomEQval() {
    for (var i = 0; i < customEQValueList.length; i++) {
      customEQValueList[i]['gain'].value = 0.0;
    }
  }
  

  @override
  final RxList<Map> dataList = RxList<Map>([]);
  @override
  final RxInt usbPlayIndex = 0.obs;
  @override
  final RxInt dataTot = 0.obs;
  @override
  final RxInt dataKey = 0.obs;

  @override
  Future<void> getSongListForUSB() async {
    try {
      if (dataTot.value == 0) return;
      if (dataTot.value == dataList.length) {
        for (var d in dataList) {
          if (d['name'].value == '') {
            if (sourceInput.value  == 'USBDAC' && project.contains('A100')) sendMsgToSocket('MCU+PAS+RAKOIT:FWD:DSK:LST:${d['id']}:${d['id']}&');
            if (sourceInput.value == 'USBPLAY') sendMsgToSocket('MCU+PAS+RAKOIT:DSK:LST:${d['id']}:${d['id']}&');
          }
        }
        return; 
      }
      String remoteId = device.remoteId.toString();
      String storageKey = '$remoteId:USB:SONGLIST';
      if (StorageClass.getStorage(storageKey) != null) {
        final songList = json.decode(StorageClass.getStorage(storageKey));
        if (songList.length == dataTot.value) {
          Log.d('同一首');
          RxList<Map> newList = RxList<Map>([]);
          for (var el in songList) {
            newList.add({
              'name': '${el['name']}'.obs,
              'id': el['id'],
            });
          }
          dataList.value = newList;
          return;
        } else {
          Log.d('不同一首');
        }
      }
      const snum = 15;
      final startKey = dataKey.value;
      final endKey = (startKey + snum >= dataTot.value) ? dataTot.value : startKey + snum;
      for (int keyNum = startKey; keyNum < endKey; keyNum++) {
        final item = {
          'id': keyNum,
          'name': ''.obs
        };
        if (!dataList.contains(item)) dataList.add(item);
        if (sourceInput.value  == 'USBDAC' && project.contains('A100')) sendMsgToSocket('MCU+PAS+RAKOIT:FWD:DSK:LST:$keyNum:${keyNum + 1}&');
        if (sourceInput.value == 'USBPLAY') sendMsgToSocket('MCU+PAS+RAKOIT:DSK:LST:$keyNum:${keyNum + 1}&');
        await Future.delayed(const Duration(milliseconds: 33));
        // 确保数据已被接收，如果不是，则重发请求
        while (dataList[keyNum]['name'].value == '') {
          if (isBLEConnected.value == false) break;
          if (sourceInput.value == 'USBDAC' && project.contains('A100')) return;
          // sendMsgToSocket('MCU+PAS+RAKOIT:FWD:DSK:LST:$keyNum:${keyNum + 1}&');
          if (sourceInput.value  == 'USBDAC' && project.contains('A100')) sendMsgToSocket('MCU+PAS+RAKOIT:FWD:DSK:LST:$keyNum:${keyNum + 1}&');
          if (sourceInput.value == 'USBPLAY') sendMsgToSocket('MCU+PAS+RAKOIT:DSK:LST:$keyNum:${keyNum + 1}&');
          await Future.delayed(const Duration(milliseconds: 11));
        }
      }
      // 更新dataKey的值
      dataKey.value = endKey;
    } catch (e) {
      Log.d('数据错误，重置');
      dataKey.value = 0;
      dataList.clear();
      Future.delayed(const Duration(milliseconds: 500),()=> getSongListForUSB());
    }
  }

  @override
  void clearAllUSBinfo(){
    dataList.clear();
    usbPlayIndex.value = 0;
    dataTot.value = 0;
    dataKey.value = 0;
  }
  
  // 是否处于配网状态
  @override
  RxBool wifiSetupState = false.obs;
  // 当前配网的步骤
  @override
  RxInt wifiSetupIndex = 1.obs;
  // 当前配网的信息的长度，用来拼接的数据的时候对接长度
  @override
  RxInt wifiSetMsgLength = 0.obs;
  // 当前配网信息的数据内容
  @override
  RxList<int> wifiSetMsg = RxList<int>([]);
  // 当前配网缓环节搜索到的wifi
  @override
  RxList wifiScanList = RxList([]);
  // 当前配网过程选择的wifi
  @override
  RxMap selectWiFi = RxMap({});
  // 当前是否正在连接wifi
  @override
  RxBool wifiConnectState = false.obs;
  // WIFI连接结果如何
  @override
  RxInt wifiConnectResult = 0.obs;
  
  @override
  void connectDevice() async {
    try{
      await device.connect(timeout: const Duration(seconds: 10));
      await Future.delayed(const Duration(milliseconds: 100));
      if (device.isConnected) {
        await onServices2();
        if (write2 != null) listenBLEStateChange();
        await Future.delayed(const Duration(milliseconds: 100));
        setupWifiThe1001();
      } else {
        // 连接失败需要移除此设备
        Log.e('${device.platformName} xxx连接失败');
        Get.back();
      }
    } catch(err) {
      Log.e('$err xxx连接失败');
    }
  }

  setupWifiThe1001() async {
    sendMsgToBLE2(byteMsg: [0x50,0x4c,0x01,0x10,0x00,0x00]);
    await Future.delayed(const Duration(seconds: 15),() {
      if (wifiScanList.isEmpty) {
        sendMsgToBLE2(byteMsg: [0x50,0x4c,0x01,0x10,0x00,0x00]);
      }
    });
    await Future.delayed(const Duration(seconds: 15),() {
      if (wifiScanList.isEmpty) {
        AppToast.show('toast2'.tr);
        disconnectBLE();
        if (Get.currentRoute == '/home' || Get.currentRoute == '/') return;
        Get.back();
      }
    });
  }

  setupWifiThe1002() async {
    wifiSetupIndex.value = 2;
    sendMsgToBLE2(byteMsg: [0x50,0x4c,0x07,0x10,0x00,0x00]);
  }

  @override
  void refreshWiFi() async {
    if (wifiScanList.isEmpty) return;
    wifiScanList.clear();
    await Future.delayed(const Duration(milliseconds: 200));
    setupWifiThe1002();
  }

  final RxString typeModel = ''.obs;

  @override
  void connectToSlecetWiFi({
    required String ssid,
    required String pwd
  }) {
    wifiSetupIndex.value = 5;
    String pas = Comm.stringToHex(pwd);
    if(typeModel.value == 'FF31'){
      ssid = Comm.hexStrToString(ssid);
      pas = Comm.hexStrToString(pas);
    }
    String msg = '{"identity":"","ssid":"$ssid","encry":"","pass":"$pas","auth":"WPA2PSK"}';
    setupWifiThe1003(msg);
  }

  void setupWifiThe1003(String msg) async {
    List<int> tag = [0x50, 0x4c];
    List<int> com = [0x02, 0x10];
    List<int> comCode = [0x03, 0x10];
    String code = '{"code":"CN"}';
    List<int> lenCode = Comm.calculateLengthBytes(code);
    List<int> datCode = Comm.convertStringToBytes(code);
    sendMsgToBLE2(byteMsg: tag + comCode + lenCode + datCode);
    await Future.delayed(const Duration(milliseconds: 500),() async {
      List<int> len = Comm.calculateLengthBytes(msg);
      List<int> dat = Comm.convertStringToBytes(msg);
      sendMsgToBLE2(byteMsg: tag + com + len + dat);
    });
    // 超时
    await Future.delayed(const Duration(seconds: 60),() { 
      if (wifiConnectResult.value != 1) {
        wifiSetupIndex.value = 3;
        AppToast.show('wrong_password'.tr);
      }
    });
  }

  RxBool btmState = false.obs;

  final RxMap btm1 = RxMap({
    'sink': '',
    'name': '',
    'codec': '',
    'state': ''
  });

  final RxMap btm2 = RxMap({
    'sink': '',
    'name': '',
    'codec': '',
    'state': ''
  });

  void getBtm1() => sendMsgToSocket('MCU+PAS+RAKOIT:BTM:0&');
  void getBtm2() => sendMsgToSocket('MCU+PAS+RAKOIT:BTM:1&');


  void randomGetBTM() async {
    getBtm1();
    getBtm2();
    await Future.delayed(const Duration(seconds: 5), (){
      if (!socketState.value) return;
      randomGetBTM();
    });
  }

  initSetting() async {
    await Future.delayed(const Duration(milliseconds: 0),(){
      sendMsgToSocket('MCU+PAS+RAKOIT:LED&');
      sendMsgToSocket('MCU+PAS+RAKOIT:VST&');
    });
    await Future.delayed(const Duration(milliseconds: 200),(){
      sendMsgToSocket('MCU+PAS+RAKOIT:MMC&');
      sendMsgToSocket('MCU+PAS+RAKOIT:BEP&');
    });
    await Future.delayed(const Duration(milliseconds: 200),(){
      sendMsgToSocket('MCU+PAS+RAKOIT:ARC&');
      sendMsgToSocket('MCU+PAS+RAKOIT:TLM&');
    });
  }

  final RxBool arcMode = false.obs;
  final RxBool tlmMode = false.obs;

  final RxBool showLED = false.obs;
  final RxBool showMMC = false.obs;
  final RxBool showBEP = false.obs;
  final RxBool showARC = false.obs;
  final RxBool showTLM = false.obs;

  void btmOn() {
    sendMsgToSocket('MCU+PAS+RAKOIT:KEY:BT_PAIR&');
    btmState.value = true;
    Future.delayed(const Duration(seconds: 15),()=> btmState.value = false);
  }

  final RxBool phonoMode = false.obs;
}