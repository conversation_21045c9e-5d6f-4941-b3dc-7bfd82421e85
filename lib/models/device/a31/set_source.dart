import 'package:get/get.dart';
import 'package:gocontrol/common/comm.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';

mixin SetDeviceSource on AbstractNET {
  // 输入源--------------[start]---------------
  @override // 当前选择输入源
  RxString sourceInput = ''.obs; 
  @override // 输入源列表
  RxList<Map> sourceInputList = RxList<Map>([]); 
  @override // 选择切换的输入源
  RxString newSourceInput = ''.obs;
  @override // 正在切换输入源
  RxBool swingSRC = false.obs;
  @override
  RxInt plmSource = RxInt(-1);
  @override
  RxList<Map> plmSourceInputList = RxList<Map>([]);


  @override // 获取输入源列表
  void getSourceList() async {
    sendMsgToSocket('MCU+PAS+RAKOIT:LST&');
    await Future.delayed(const Duration(seconds: 2),() {
      if (sourceInputList.isEmpty) sendMsgToSocket('MCU+PAS+RAKOIT:LST&');
    });
  }

  @override // 获取当前输入源
  void getSourceInput() async {
    if (rakoitAPI.value) {
      sendMsgToSocket('MCU+PAS+RAKOIT:SRC&');
      await Future.delayed(const Duration(seconds: 2),() {
        if (sourceInput.value == '') getSourceInput();
      });
    } else {
      sendMsgToSocket('MCU+PLM+GET');
      await Future.delayed(const Duration(seconds: 2),() {
        if (sourceInput.value == '') sendMsgToSocket('MCU+PLM+GET');
      });
    }
  }

  @override // 设置当前输入源
  void setSourceInput(String src){
    if(origin == Origins.ble) return sendMsgToBLE(msg: 'SRC:$src;');
    if(rakoitAPI.value){
      sendMsgToSocket('MCU+PAS+RAKOIT:SRC:$src&');
    }else{
      // 暂未
      Log.d(src);
      String code = '000';
      if(src == 'BT'){
        code = '006';
      }else if(src == 'LINE-IN'){
        code = '005';
      }else if(src == 'LINE-IN2'){
        code = '012';
      }else if(src == 'OPT'){
        code = '008';
      }else if(src == 'COAX'){
        code = '010';
      }else if(src == 'HDMI'){
        code = '015';
      }
      sendMsgToSocket('MCU+PLM+$code');
    }
    Future.delayed(const Duration(milliseconds: 200),()=> getSourceInput());
  }

  @override
  void plmGetSource(){
    // location 
    List<int> rxList = Comm.findOnesInHex(plmSource.value);
    List<int> order = [3,2,9,17,5,11,16]; // USBPLAY COAX
    rxList.sort((a, b) {
      int indexA = order  .indexOf(a);
      int indexB = order.indexOf(b);
      if (indexA == -1 && indexB == -1) {
        return 0;
      } else if (indexA == -1) {
        return 1;
      } else if (indexB == -1) {
        return -1;
      } else {
        return indexA.compareTo(indexB); // 按照指定顺序列表的顺序进行排序
      }
    });
    plmSourceInputList.add({
      'id': 'NET',
      'name': 'Music',
      'icon': GoIcons.srcWiFi
    });
    for (var e in rxList) {
      if(e == 2){
        plmSourceInputList.add({
          'id': 'LINE-IN',
          'name': 'AUX',
          'icon': GoIcons.srcAux
        });
      }else if(e == 3){
        plmSourceInputList.add({
          'id': 'BT',
          'name': 'BT',
          'icon': GoIcons.srcBT
        });
      }else if(e == 5){
        plmSourceInputList.add({
          'id': 'OPT',
          'name': 'OPT',
          'icon': GoIcons.srcOPT
        });
      }else if(e == 9){
        plmSourceInputList.add({
          'id': 'LINE-IN2',
          'name': 'AUX2',
          'icon': GoIcons.srcAux
        });
      }else if(e == 11){
        plmSourceInputList.add({
          'id': 'HDMI',
          'name': 'HDMI',
          'icon': GoIcons.srcARC
        });
      }else if(e == 16){
        plmSourceInputList.add({
          'id': 'USBDAC',
          'name': 'DAC',
          'icon': GoIcons.srcDAC
        });
      }else if(e == 17){
        plmSourceInputList.add({
          'id': 'PHONO',
          'name': 'PHONO',
          'icon': GoIcons.srcDAC
        });
      }
    }
  }
  // 输入源--------------[end]---------------
}