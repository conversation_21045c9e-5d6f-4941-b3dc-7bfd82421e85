// ignore_for_file: constant_identifier_names

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:bwee_interface/bwee_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:gbk_codec/gbk_codec.dart';
import 'package:get/get.dart';
import 'package:gocontrol/api/api.dart';
import 'package:gocontrol/common/libre_msg.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/common/take_bgcolor.dart';
import 'package:gocontrol/common/toast.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/models/model_class/a4_usblist.dart';
import 'package:gocontrol/models/model_class/a5_playinfo.dart';
import 'package:gocontrol/models/model_class/set_network.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/routes/routes.dart';

import 'bwee/bwee_controller.dart';
import 'i_tcp_socket.dart';
import 'next_prve_state_machine.dart';

class Libre extends DeviceBase
    with
        BaseAudio,
        A4Usblist,
        A5Playinfo,
        SetDeviceNetWork,
        NextPrveStateMachineMixin,
        ITcpSocket,
        BweeController {
  Libre();

  // BLE模式下
  Libre.connectTheBLE(BluetoothDevice ble) {
    device = ble;
    origin = Origins.ble;
  }

  // 网络模式下
  Libre.connectTheNetwork(List<String> result) {
    origin = Origins.net;
    skipGcast.value = StorageClass.getStorage('${name.value}&NFG') ?? false;
    // 解析数据
    List<Map> info = result.map((el) {
      int spl = el.indexOf(':');
      if (spl == -1) return {'unknow': ''};
      String nam = el.substring(0, spl);
      String val = el.substring(spl + 1, el.length);
      final map = {nam: val};
      return map;
    }).toList();
    // 信息同步
    for (Map item in info) {
      if (item.containsKey('ip')) ip.value = item['ip'];
      if (item.containsKey('DeviceName')) name.value = item['DeviceName'];
      if (item.containsKey('NETMODE')) netmode.value = item['NETMODE'];
      // if (item.containsKey('FWVERSION')) version.value = item['FWVERSION'];
    }
    //bwee module
    //注入bwee控制器
    if (!Get.isRegistered<IBweeController>(tag: ip.value)) {
      deviceName = name.value;
      Get.lazyPut<IBweeController>(() => this, tag: ip.value);
    }
    // 注入bwee逻辑
    if (!Get.isRegistered<BweeLogic>(tag: ip.value)) {
      Get.lazyPut<BweeLogic>(() => BweeLogic(deviceIP: ip.value), tag: ip.value);
    }
  }

  // -- 基础信息 --
  @override
  Origins origin = Origins.ble;

  @override
  RxString name = ''.obs;

  @override
  RxString version = ''.obs;

  final deviceMode = ''.obs;

  final RxBool skipGcast = false.obs;

  @override
  void getVersion() {}

  @override
  void getDeviceName() {}

  @override
  void setDeviceName(String name) {}
  // -- 基础信息 --·

  // -- 音量 --
  @override
  RxDouble volume = 0.0.obs;

  RxBool mute = false.obs;

  @override
  void getVolume() {
    sendMsgToTcpSocket('VOL;');
  }

  @override
  void setVolume(double volume) {
    sendMsgToTcpSocket('VOL:${volume.round()};');
  }

  void getMute() {
    luciSocket?.add(ApiManage.luciApi.lsxInfo(
      data: 'MUTE',
      command: 0x0028,
    ));
  }

  void setMute(bool mute) {
    String mm = mute ? 'MUTE' : 'UNMUTE';

    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: mm, command: 0x0028, commandType: 0x02));

    if (!mute) sendMsgToTcpSocket('VOL;');
  }
  // -- 音量 --

  // -- 输入源 --
  @override
  RxString sourceInput = ''.obs;

  @override
  RxList<Map> sourceInputList = RxList<Map>([]);

  @override
  RxString newSourceInput = ''.obs;

  @override
  RxBool swingSRC = false.obs;

  RxString sourceCurrent = '0'.obs;

  @override
  void getSourceList() => sendMsgToTcpSocket('LST;');

  @override
  void getSourceInput() => sendMsgToTcpSocket('SRC;');

  @override
  void setSourceInput(String src) {
    sendMsgToTcpSocket('SRC:$src;');
    Future.delayed(
        const Duration(milliseconds: 400), () => sendMsgToTcpSocket('VOL;'));
  }

  // -- 输入源 --

  // device
  StreamSubscription? tcpSubStream;

  Socket? luciSocket;
  RxString ip = ''.obs;
  RxString ip2 = ''.obs;
  RxString netmode = ''.obs;
  RxString netInfo = ''.obs;
  RxBool tcpSocketState = false.obs;
  RxBool luciSocketState = false.obs;

  RxInt updateState = 0.obs;
  ValueNotifier<double> donwloadVerVal = ValueNotifier(0.0);
  RxBool resetState = false.obs;
  Timer? resetTimer;
  Timer? wakeUpTimer;

  bool needWakeUp = false;

  void initNetwork({bool rev = false}) async {
    closeAll();

    await createSocketToLUCI();

    // 更新设备状态
    getGast();

    if (updateState.value == 4) {
      await Future.delayed(const Duration(seconds: 1));
      updateState.value = 99;
      Log.d('updateState.value = 99');
    }

    // Log.y('${name.value} 初始化完成');
    if (!rev) homCon.addToDeviceList(this);

    await Future.delayed(const Duration(milliseconds: 300));
    await createSocketToTCP();

    if (homCon.libreUpList.contains(name.value)) {
      homCon.libreUpList.remove(name.value);
      checkOneSec = true;
      Timer.periodic(const Duration(seconds: 60), (timer) {
        if (checkOneSec) {
          Log.y('超时了！！！！');
          homCon.libreSetUpName?.wifiSetupIndex.value = 7;
          homCon.libreSetUpName = null;
          checkOneSec = false;
          timer.cancel();
        } else {
          timer.cancel();
        }
      });

      //   await Future.delayed(const Duration(seconds: 5));
      //   Log.y('${name.value} 需要 UPdateVer');
      //   Log.y('先发送一条消息看看设备连接是否成功');
      //   getVolume();
      //   await Future.delayed(const Duration(milliseconds: 300));
      //   getMute();
      //   Log.w('最后发送UPdatever');
      //   await Future.delayed(const Duration(seconds: 1));
      //   getUpdateVer();
    }
  }

  bool wakeUp = false;
  bool checkOneSec = false;

  // TCP Socket
  Future<void> createSocketToTCP({bool argin = false}) async {
    try {
      tcpSocket = await Socket.connect(ip.value, 2018);
      tcpSocketState.value = true;
      wakeUp = false;

      tcpSubStream = tcpSocket?.listen((value) {
        try {
          LibreMsg.getMsg(utf8.decode(value), this, value);
          onMsgReceived(utf8.decode(value));
        } catch (e) {
          LibreMsg.getMsg(gbk_bytes.decode(value), this, value);
          onMsgReceived(gbk_bytes.decode(value));
        }
      }, onDone: () {
        tcpSocketState.value = false;
      }, onError: (e) {
        tcpSocketState.value = false;
      });

      //更新bwee controller的socket
      if (Get.isRegistered<IBweeController>(tag: ip.value)) {
        (Get.find<IBweeController>(tag: ip.value) as ITcpSocket).tcpSocket =
            tcpSocket;
      }
    } catch (e) {
      Log.y('$ip TCP连接失败');
    }
    initInfoDataForTCP();
    Future.delayed(const Duration(seconds: 6), () async {
      if (tcpSocket == null) {
        Log.d('${name.value} TCP未连接');
        // await Future.delayed(const Duration(milliseconds: 300));
        // await createSocketToTCP();
      }
    });
  }

  StreamSubscription? streamLuci;

  // LUCI Socket
  Future<void> createSocketToLUCI() async {
    if (ip.value == '') ip.value = ip2.value;
    try {
      ByteData cert = await rootBundle.load('cert/client.pem');
      ByteData key = await rootBundle.load('cert/client.key');
      SecurityContext securityContext = SecurityContext();
      securityContext.useCertificateChainBytes(cert.buffer.asUint8List(),
          password: '');
      securityContext.usePrivateKeyBytes(key.buffer.asUint8List(),
          password: '');
      luciSocket = await SecureSocket.connect(ip.value, 7777,
          onBadCertificate: (X509Certificate cert) => true,
          context: securityContext);
      Log.r('${name.value} - 使用配置文件创建LUCI Socket');
      luciSocketState.value = true;
      streamLuci = luciSocket?.listen((value) {
        LibreMsg.getLuciMsg(value, this);
      }, onDone: () {
        luciSocketState.value = false;
      }, onError: (e) {
        luciSocketState.value = false;
      });
      final appInfo = ApiManage.luciApi.lsxInfo(
          data:
              '{"APP_info":{"id":"com.rakoit.gocontrol","version":"2.0.7","ip":"${ApiManage.luciApi.ipv4ip}"}}',
          command: 0x0003);
      luciSocket?.add(appInfo);
    } catch (e) {
      try {
        luciSocket = await Socket.connect(ip.value, 7777);
        luciSocketState.value = true;
        Log.r('${name.value} - 不需要配置文件创建LUCI Socket');
        streamLuci = luciSocket?.listen((value) {
          LibreMsg.getLuciMsg(value, this);
        }, onDone: () {
          luciSocketState.value = false;
        }, onError: (e) {
          luciSocketState.value = false;
        });
        final appInfo = ApiManage.luciApi.lsxInfo(
            data:
                '{"APP_info":{"id":"com.rakoit.gocontrol","version":"2.0.7","ip":"${ApiManage.luciApi.ipv4ip}"}}',
            command: 0x0003);
        luciSocket?.add(appInfo);
      } catch (e) {
        Log.b('$ip 连接失败');
      }
    }

    await Future.delayed(
        const Duration(milliseconds: 200), () => getPlayInfo());
    await Future.delayed(const Duration(milliseconds: 200), () => getGast());
  }

  void getInChangeNameOff() {
    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: 'get', command: 0x0231, commandType: 0x02));
  }

  void getNetworkInfo() {
    String mode = netmode.value == 'WLAN' ? 'MACADDR:wlan0' : 'MACADDR:eth0';
    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: mode, command: 0x005B, commandType: 0x02));
  }

  void sendMsgToTcpSocket(String msg) {
    if (tcpSocket == null) return;
    try {
      Log.d('${name.value} 发送数据：$msg');
      tcpSocket?.add(utf8.encode(msg));
    } catch (e) {
      tcpSocket?.close();
      tcpSocket = null;
      if (tcpSubStream != null) {
        tcpSubStream!.cancel();
        tcpSubStream = null;
      }
      createSocketToTCP();
    }
  }

  void initInfoDataForTCP() async {
    await Future.delayed(const Duration(milliseconds: 300), () {
      sendMsgToTcpSocket('VOL;');
      Timer(const Duration(milliseconds: 500), () {
        if (volume.value == 0) sendMsgToTcpSocket('VOL;');
      });
    });

    await Future.delayed(const Duration(milliseconds: 300), () {
      sendMsgToTcpSocket('VER;');
      Timer(const Duration(milliseconds: 500), () {
        if (volume.value == 0) sendMsgToTcpSocket('SRC;');
      });
    });

    if (sourceInputList.isEmpty) {
      await Future.delayed(const Duration(milliseconds: 300), () {
        sendMsgToTcpSocket('LST;');
        Timer(const Duration(milliseconds: 500), () {
          if (volume.value == 0) sendMsgToTcpSocket('LST;');
        });
      });
    }

    await Future.delayed(const Duration(milliseconds: 300), () {
      sendMsgToTcpSocket('SRC;');
      Timer(const Duration(milliseconds: 500), () {
        if (volume.value == 0) sendMsgToTcpSocket('SRC;');
      });
    });

    await supportBwee;
  }

  // -- BLE --
  BluetoothDevice device =
      BluetoothDevice(remoteId: const DeviceIdentifier(''));
  BluetoothCharacteristic? write;
  BluetoothCharacteristic? notify;
  final isBLEConnected = false.obs;
  // -- BLE --

  // -- 配网 --
  List<int> scanWifiList = [];
  int scanWifiListLen = 0;
  RxList<Widget> netWorkList = RxList<Widget>([]);
  RxInt connectWifiInx = RxInt(-1);

  friendlyName() {
    List<int> requestData = [0xAB, 0x05, 0x00, 0x00, 0xCD];
    sendBleToMsg(requestData);
  }

  sendBleToMsg(List<int> data) {
    try {
      write!
          .write(data, withoutResponse: write!.properties.writeWithoutResponse);
    } catch (e) {
      Log.e('发送BLE数据失败：$e');
    }
  }
  // -- 配网 --

  RxString timeZone = ''.obs;

  RxBool inAppleHome = false.obs;

  // -- 播放控制 --
  @override
  void nextPlay() async {
    if (sourceInput.value == 'USBPLAY') {
      tcpSocket?.add('NXT;'.codeUnits);
      await Future.delayed(const Duration(milliseconds: 600),
          () => sendMsgToTcpSocket('DSK:CUR;'));
      return;
    }

    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: 'NEXT', command: 0x0028, commandType: 0x02));

    // Future.delayed(const Duration(milliseconds: 500),(){
    //   getPlayInfo();
    // });
  }

  @override
  void prevPlay() async {
    if (sourceInput.value == 'USBPLAY') {
      tcpSocket?.add('PRE;'.codeUnits);
      await Future.delayed(const Duration(milliseconds: 600),
          () => sendMsgToTcpSocket('DSK:CUR;'));
      return;
    }

    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: 'PREV', command: 0x0028, commandType: 0x02));

    // Future.delayed(const Duration(milliseconds: 300),(){
    //   getPlayInfo();
    // });
  }

  @override
  void pauseSong() {}

  @override
  void playAndPauseSong() {}

  @override
  void playSong() async {
    // if (sourceInput.value == 'USB' && !isPlaying.value && songName.value != '') {
    //   luciSocket?.add(ApiManage.luciApi.lsxInfo(
    //     data: 'ONETOUCH',
    //     command: 0x0029,
    //     commandType: 0x02
    //   ));
    //   return;
    // }

    if (sourceInput.value == 'LINE-IN') {
      if (mute.value) {
        setMute(false);
      } else {
        setMute(true);
      }
      return;
    }

    if (sourceInput.value == 'USBPLAY') {
      tcpSocket?.add('POP;'.codeUnits);
      Future.delayed(
          const Duration(milliseconds: 200), () => sendMsgToTcpSocket('PLA;'));
      return;
    }

    final data = isPlaying.value ? 'PAUSE' : 'PLAY';

    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: data, command: 0x0028, commandType: 0x02));

    if (sourceInput.value == 'BT') {
      isPlaying.value = !isPlaying.value;
      return;
    }

    // await Future.delayed(const Duration(milliseconds: 600));
    // getPlayInfo();
  }

  void playRadio(String url) {
    luciSocket?.add(ApiManage.luciApi.lsxInfo(
        data: 'PLAYITEM:DIRECT:$url', command: 0x0029, commandType: 0x02));
  }

  void getTimeZone() {
    luciSocket?.add(ApiManage.luciApi.lsxInfo(
      data: '',
      command: 0x023D,
    ));
    Log.d('get time zone');
  }

  void setTimeZone(String zone) {
    luciSocket?.add(ApiManage.luciApi.lsxInfo(
        data: '{"timezone_oem":"$zone","time_in_12_hour_format":false}',
        command: 0x023D,
        commandType: 0x02));

    getTimeZone();
  }

  void reportFeedBack(String data) {
    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: data, command: 0x028B, commandType: 0x02));
  }

  // -- 播放控制 --

  // xxxxxxxxxxxx
  @override
  RxString updateStr = ''.obs;
  @override
  RxBool updateVer = false.obs;
  @override
  RxBool led = false.obs;
  @override
  RxInt vst = 0.obs;
  @override
  void vstAdd() {}
  @override
  void vstSub() {}
  @override
  void updateConfirm() {}

  @override
  void getUpdateVer() {
    Log.d('QUERY_UPDATE');
    // luciSocket?.add(ApiManage.luciApi.lsxInfo(
    //   data: 'QUERY_UPDATE',
    //   // data: 'CHECKING_UPDATE',
    //   command: 0x00EB,
    //   commandType: 0x02
    // ));
  }

  @override
  void resetDevice() {}

  void closeAll({bool tcp = false}) {
    if (tcpSocket != null) {
      tcpSocket?.close();
      tcpSocket = null;
    }
    if (tcpSubStream != null) {
      tcpSubStream?.cancel();
      tcpSubStream = null;
    }
    if (tcp) return;
    if (luciSocket != null) {
      luciSocket?.close();
      luciSocket = null;
    }
    if (streamLuci != null) {
      streamLuci!.cancel();
      streamLuci = null;
    }
  }

  void dispose() {
    if (notifyStream != null) notifyStream!.cancel();
    if (tcpSubStream != null) tcpSubStream!.cancel();
    if (streamLuci != null) streamLuci!.cancel();
    if (tcpSocket != null) tcpSocket?.close();
    if (luciSocket != null) luciSocket?.close();
    if (device.isConnected) device.disconnect();
  }

  List<int> convertToHex(String input) {
    List<int> bytes = input.codeUnits;
    List<int> hexBytes = List<int>.from(bytes.map((byte) => byte));
    hexBytes.insert(0, bytes.length);
    return hexBytes;
  }

  List<int> calculateFinalDataLength(List<int> data) {
    int length = data.length;
    int byte1 = length ~/ 256; // 高位字节
    int byte2 = length % 256; // 低位字节
    // 返回两个字节的十六进制形式作为一个字节列表
    return [byte2, byte1];
  }

  disconnectBLE() async {
    await device.disconnect();
  }

  final RxBool setNetworking = false.obs;

  StreamSubscription? bleStateStream;

  //
  void listenDevState() async {
    bleStateStream =
        device.connectionState.listen((BluetoothConnectionState event) {
      // 设备的连接情况
      if (event == BluetoothConnectionState.connected) {
        isBLEConnected.value = true;
        Log.d('设备已连接');
      } else if (event == BluetoothConnectionState.disconnected) {
        isBLEConnected.value = false;
        if (notifyStream != null) {
          notifyStream!.cancel();
          notifyStream = null;
        }
        if (bleStateStream != null) {
          bleStateStream!.cancel();
          bleStateStream = null;
        }
      }
    });
  }

  // 发送交互音
  sendNotification() {
    List<int> requestData = [0xAB, 0x04, 0x00, 0x00, 0xCD];
    sendBleToMsg(requestData);
  }

  // 退出待机模式
  exitStandby() {
    List<int> requestData = [0xAB, 0x22, 0x00, 0x00, 0xCD];
    sendBleToMsg(requestData);
  }

  // 搜索wifi未压缩
  void getWifiScanList() {
    List<int> requestData = [0xAB, 0x00, 0x00, 0x00, 0xCD];
    sendBleToMsg(requestData);
  }

  @override
  void refreshWiFi() async {
    if (wifiScanList.isEmpty) return;
    wifiScanList.clear();
    await Future.delayed(const Duration(milliseconds: 200));
    getWifiScanList();
  }

  // 取消ble连接
  stopBle() {
    List<int> requestData = [0xAB, 0x03, 0x00, 0x00, 0xCD];
    sendBleToMsg(requestData);
  }

  @override
  RxString songName = ''.obs;
  @override
  RxString songArtist = ''.obs;
  @override
  RxString songAlbum = ''.obs;
  @override
  RxString songImage = ''.obs;
  @override
  RxDouble playLengths = 0.1.obs;
  @override
  RxDouble totTalVal = 0.1.obs;

  //
  RxBool isPlaying = false.obs;
  RxBool tos = false.obs;
  RxBool seek = false.obs;
  RxString sampleRate = ''.obs;
  RxString mime = ''.obs;

  @override
  Future<void> getPlayInfo() async {
    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: 'GETUI:PLAY', command: 0x0029, commandType: 0x02));

    Future.delayed(const Duration(milliseconds: 100), () {
      List<int> st = ApiManage.luciApi
          .lsxInfo(data: '', command: 0x003F, commandType: 0x01);
      luciSocket?.add(st);
    });
  }

  final RxBool pairGoToGcast = false.obs;
  final RxBool needGoToGast = false.obs;

  void getGast() async {
    Map a = {
      "request_type": "get",
      "id": "status",
      "device_uuid": homCon.deviceUUID
    };
    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: json.encode(a), command: 0x023B, commandType: 0x02));
  }

  void gcastAndAccept() async {
    Map a = {
      "request_type": "set",
      "id": "tos",
      "action": "accepted",
      "user_ip": ApiManage.luciApi.ipv4ip,
      "device_uuid": homCon.deviceUUID
    };
    if (luciSocket == null) {
      await createSocketToLUCI();
      gcastAndAccept();
    } else {
      luciSocket?.add(ApiManage.luciApi
          .lsxInfo(data: json.encode(a), command: 0x023B, commandType: 0x02));
    }
  }

  void sendCrashReport(bool na) {
    Map a = {
      "request_type": "set",
      "id": "crash_report",
      "action": na ? "accepted" : "declined",
      "user_ip": ApiManage.luciApi.ipv4ip,
      "device_uuid": homCon.deviceUUID
    };
    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: json.encode(a), command: 0x023B, commandType: 0x02));
  }

  RxBool crashReport = false.obs;

  RxBool nextBtn = false.obs;
  RxBool prevBtn = false.obs;

  @override
  RxDouble intensity = 0.0.obs;

  @override
  RxDouble maxVolume = 3.0.obs;

  @override
  RxDouble treble = 0.0.obs;

  @override
  RxDouble mid = 0.0.obs;

  @override
  RxDouble balance = 0.0.obs;

  @override
  RxDouble bass = 0.0.obs;

  @override
  RxBool crossover = false.obs;

  @override
  RxList<Map> customEQList = RxList<Map>([]);

  @override
  List<Map> customEQValueList = [];

  @override
  RxInt customEQindex = 0.obs;

  @override
  RxBool deepBass = false.obs;

  @override
  void initAudioData() async {
    await Future.delayed(const Duration(milliseconds: 100), () => getTreble());
    await Future.delayed(const Duration(milliseconds: 100), () => getMid());
    await Future.delayed(const Duration(milliseconds: 100), () => getBass());
  }

  @override
  void delectCustomEQ() {}
  @override
  void customEQListValReset() {}

  @override
  void clearCustomEQval() {}

  @override
  RxInt eqIndex = 0.obs;

  @override
  RxList<Map> eqList = RxList<Map>([]);

  @override
  RxDouble frequency = 0.0.obs;

  @override
  void getBalance() {}

  @override
  void getBass() {}

  @override
  void getCEQandFLT(String val) {}

  @override
  void getCrossover() {}

  @override
  void getCustomEQList() {}

  @override
  void getDeepBass() {}

  @override
  void getEqIndex() {}

  @override
  void getEqList() {}

  @override
  void getFrequency() {}

  @override
  void getIntensity() {}

  @override
  void getMaxVolume() {}

  @override
  void getTreble() {
    sendMsgToTcpSocket('TRE;');
  }

  @override
  void getMid() {
    sendMsgToTcpSocket('MID;');
  }

  @override
  void setBass(double val) {
    sendMsgToTcpSocket('BAS;');
  }

  @override
  void initCustomEQval() {}

  @override
  void saveCustomEQ() {}

  @override
  void setBalance(double val) {}

  @override
  void setCrossover(bool val) {}

  @override
  void setDeepBass(bool val) {}

  @override
  void setEqIndex(int index) {}

  @override
  void setEqList(String str, {bool? custom}) {}

  @override
  void setFLT(String val) {}

  @override
  void setFrequency(double val) {}

  @override
  void setIntensity(double val) {}

  @override
  void setMaxVolume(double val) {}

  @override
  void setMid(double val) {
    sendMsgToTcpSocket('MID:${val.ceil()};');
  }

  @override
  void setTreble(double val) {
    sendMsgToTcpSocket('TRE:${val.ceil()};');
  }

  void sendSeek(double value) {
    luciSocket?.add(ApiManage.luciApi.lsxInfo(
        data: 'SEEK:${(value * 1000).toStringAsFixed(0)}',
        command: 0x0028,
        commandType: 0x02));
  }

  final RxBool spotifyPodCast = false.obs;

  @override
  final RxList<Map> dataList = RxList<Map>([]);

  final Set<Map> dataSetList = {};

  @override
  final RxInt usbPlayIndex = 0.obs;
  @override
  final RxInt dataTot = 0.obs;
  @override
  final RxInt dataKey = 0.obs;
  final RxString usbNowSongItem = ''.obs;

  @override
  Future<void> getSongListForUSB() async {
    try {
      if (dataTot.value == dataList.length) return;

      if (dataTot.value == 0) {
        dataList.clear();
        dataSetList.clear();
        tcpSocket?.add('DSK:TOT;'.codeUnits);
        return;
      }

      String storageKey = '${name.value}:USB:SONGLIST';
      Log.p(storageKey);
      if (StorageClass.getStorage(storageKey) != null) {
        final storageSongList =
            json.decode(StorageClass.getStorage(storageKey));
        if (storageSongList.length == dataTot.value) {
          RxList<Map> newList = RxList<Map>([]);
          for (var el in storageSongList) {
            newList.add({
              'name': '${el['name']}'.obs,
              'id': el['id'],
            });
          }
          dataList.value = newList;
          return;
        }
      }

      const snum = 15;
      final startKey = dataKey.value;
      final endKey =
          (startKey + snum >= dataTot.value) ? dataTot.value : startKey + snum;

      for (int keyNum = startKey; keyNum < endKey; keyNum++) {
        final item = {'id': keyNum, 'name': ''.obs};
        Log.d('创建了 $item');
        await Future.delayed(const Duration(milliseconds: 33));
        if (dataSetList.contains(item)) continue;
        dataSetList.add(item);
        dataList.add(item);
        // 发送BLE请求并等待响应
        sendMsgToTcpSocket('DSK:LST:$keyNum:${keyNum + 1}&');
        await Future.delayed(const Duration(milliseconds: 33));
        // 确保数据已被接收，如果不是，则重发请求
        while (dataList[keyNum]['name'].value == '') {
          if (isBLEConnected.value == false) break;
          sendMsgToTcpSocket('DSK:LST:$keyNum:${keyNum + 1}&');
          await Future.delayed(const Duration(milliseconds: 11));
        }
      }

      // 更新dataKey的值
      dataKey.value = endKey;
    } catch (e) {
      Log.d('数据错误，重置 $e');
      dataKey.value = 0;
      dataList.clear();
      dataSetList.clear();
      await Future.delayed(
          const Duration(milliseconds: 500), () => getSongListForUSB());
    }
  }

  @override
  void clearAllUSBinfo() {
    dataList.clear();
    dataSetList.clear();
    usbPlayIndex.value = 0;
    dataTot.value = 0;
    dataKey.value = 0;
  }

  @override
  Future<void> getMedialInfo() async {}

  @override
  Timer? startLoopTask() {
    return null;
  }

  Timer? timerLoops;

  Timer getStartTask() {
    return Timer.periodic(const Duration(milliseconds: 1000), (timer) async {
      if (playLengths.value < totTalVal.value) {
        // 增加播放长度
        playLengths.value += 1;
      } else {
        // 取消定时器
        timer.cancel();
        timerLoops = null;

        if (sourceInput.value == 'USBPLAY') {
          Future<void> sendDelayedMsg(String msg, int delay) async {
            await Future.delayed(
                Duration(milliseconds: delay), () => sendMsgToTcpSocket(msg));
          }

          await sendDelayedMsg('DSK:CUR;', 600);
          await sendDelayedMsg('DSK:CUR;', 300);
          await sendDelayedMsg('ELP;', 300);
          await sendDelayedMsg('PLA;', 300);
        }
      }
    });
  }

  void getSongListUSBforLUCI() {
    Log.w("开始发送HOME");
    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: 'GETUI:HOME', command: 0x0029, commandType: 0x02));
  }

  void selectItem(int index) {
    luciSocket?.add(ApiManage.luciApi.lsxInfo(
        data: 'SELECTITEM:$index', command: 0x0029, commandType: 0x02));
  }

  final RxInt usbTotalSongs = 0.obs;
  final RxInt usbPageIndex = 1.obs;
  int usbPages = 0;

  final usbSongList = RxList<Map<String, String>>([]);

  void upItem() {
    if (selectUsbSongLock) {
      Log.d('忙碌中');
      AppToast.show('Equipment busy');
      return;
    }

    if (usbPageIndex.value == 1) {
      Log.d('无法继续上一页，已经在第1页');
      return;
    }

    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: 'SCROLLUP', command: 0x0029, commandType: 0x02));
  }

  void downItem() {
    if (selectUsbSongLock) {
      Log.d('忙碌中');
      AppToast.show('Equipment busy');
      return;
    }

    if (usbPageIndex.value >= usbPages) {
      Log.d('无法继续下一页，已经在第${usbPageIndex.value}页');
      return;
    }

    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: 'SCROLLDOWN', command: 0x0029, commandType: 0x02));
  }

  void browseOnBack() {
    luciSocket?.add(ApiManage.luciApi
        .lsxInfo(data: 'BACK', command: 0x0029, commandType: 0x02));
  }

  bool selectUsbSongLock = false;
  int selectUsbSongIndex = 0;

  void selectMusic(String page, String index) async {
    if (selectUsbSongLock) return;
    selectItem(int.parse(index));
    selectUsbSongLock = true;
    Get.back();
  }

  // 是否处于配网状态
  @override
  RxBool wifiSetupState = false.obs;
  // 当前配网的步骤
  @override
  RxInt wifiSetupIndex = 1.obs;
  // 当前配网的信息的长度，用来拼接的数据的时候对接长度
  @override
  RxInt wifiSetMsgLength = 0.obs;
  // 当前配网信息的数据内容
  @override
  RxList<int> wifiSetMsg = RxList<int>([]);
  // 当前配网缓环节搜索到的wifi
  @override
  RxList wifiScanList = RxList([]);
  // 当前配网过程选择的wifi
  @override
  RxMap selectWiFi = RxMap({});
  // 当前是否正在连接wifi
  @override
  RxBool wifiConnectState = false.obs;
  // WIFI连接结果如何
  @override
  RxInt wifiConnectResult = 0.obs;

  StreamSubscription? notifyStream;

  Future<void> onServices() async {
    try {
      List<BluetoothService> services = await device.discoverServices();

      const keyStr = 'b8313268-90dc-5a30-bfb1-a814e7c6dbba';
      const keyChara = '04b5d61d-7d20-5122-ae91-fdc306471497';

      for (var service in services) {
        if (service.serviceUuid == Guid(keyStr)) {
          for (var chara in service.characteristics) {
            // 处理写特性
            if (chara.characteristicUuid != Guid(keyChara)) continue;
            if (chara.properties.write || chara.properties.writeWithoutResponse) {
              write = chara;
            }
            // 处理通知特性
            if (chara.properties.notify) {
              notify = chara;
              try {
                await chara.setNotifyValue(true);
              } catch (err) {
                return Future.error('notify不可用');
              }
            }
          }
        }
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 3));
      onServices();
    }
  }

  @override
  void connectDevice() async {
    try {
      await device.connect().timeout(const Duration(seconds: 15),
          onTimeout: () {
        Log.e('连接失败 1');
        AppToast.show('${'toast3'.tr} (1)');
      });
    } catch (e) {
      try {
        await device.connect().timeout(const Duration(seconds: 15),
            onTimeout: () {
          Log.e('连接失败 2');
          AppToast.show('${'toast3'.tr} (2)');
          disconnectBLE();
          if (Get.currentRoute == Routes.setNetworkPage) {
            Get.back();
          }
          return;
        });
      } catch (e) {
        Log.e('连接失败 3 $e');
        AppToast.show('${'toast3'.tr} (3)');
        disconnectBLE();
        if (Get.currentRoute == Routes.setNetworkPage) {
          Get.back();
        }
        return;
      }
    }

    await onServices();

    if (write == null || notify == null) {
      Log.e('write或者notify不可用');
      disconnectBLE();
      AppToast.show('Write or Notify is not available!');
      if (Get.currentRoute == Routes.setNetworkPage) {
        Get.back();
      }
      return;
    }

    notifyStream = notify!.onValueReceived.listen((event) {
      LibreMsg.getBleMsg(event, this);
    });

    Log.d('BLE连接成功');
    standby();
  }

  void standby() async {
    await Future.delayed(
        const Duration(milliseconds: 300), () => sendNotification());
    await Future.delayed(
        const Duration(milliseconds: 300), () => exitStandby());
    await Future.delayed(
        const Duration(milliseconds: 300), () => getWifiScanList());
  }

  @override
  void connectToSlecetWiFi({required String ssid, required String pwd}) {
    List<int> ssids = convertToHex(ssid);
    List<int> psw = convertToHex(pwd);
    List<int> security = convertToHex(selectWiFi['Security']);
    List<int> friendly = convertToHex(name.value);
    List<int> dataLength =
        calculateFinalDataLength([...ssids, ...psw, ...security, ...friendly]);
    List<int> msg = [
      0xAB,
      0x01,
      ...dataLength,
      ...ssids,
      ...psw,
      ...security,
      ...friendly,
      0xCD
    ];
    sendBleToMsg(msg);

    wifiSetupIndex.value = 5;
    Future.delayed(const Duration(seconds: 30), () {
      if (wifiConnectResult.value == 1) {
        Get.back();
        AppToast.show('toast2'.tr);
      }
    });
  }

  void setSongImage(String url) async {
    Log.y('当前设置图片地址是 $url');
    songImage.value = url;
    if (homCon.selectDevice == this) {
      TakeBgColor.getImageDominantColor(songImage.value);
    }
  }

  void getPair() {
    sendMsgToTcpSocket('KEY:BT_PAIR;');
    sendMsgToTcpSocket('BTM:0;');
  }

  void disPair() {
    sendMsgToTcpSocket('KEY:BT_RESET;');
  }

  void setPlayView(Map viewData) async {
    if (sourceInput.value == 'USB' && selectUsbSongLock) {
      browseOnBack();
      await Future.delayed(const Duration(milliseconds: 100));
      Log.d('返回');
      browseOnBack();
      await Future.delayed(const Duration(milliseconds: 300));
      browseOnBack();
      selectUsbSongLock = false;
    }

    // 设置按钮disable状态
    _setPlayBtnDisable(viewData['Seek'], viewData['Next'], viewData['Prev']);

    // 设置歌曲信息
    _setPlayInfoData(viewData);
    // 设置PodCast
    if (viewData.containsKey('PlayUrl')) {
      _setPodCastStatus(viewData['PlayUrl']);
    }
    // 设置播格式
    if (viewData.containsKey('Mime')) {
      mime.value = viewData['Mime'];
    }
    if (viewData.containsKey('SampleRate')) {
      sampleRate.value = viewData['SampleRate'];
    }
    // 设置总时间
    if (viewData.containsKey('TotalTime')) {
      final int totalTime = viewData['TotalTime'];
      if (totalTime > 0) {
        totTalVal.value = totalTime.toDouble() / 1000;
        playLengths.value = 0.001;
      } else {
        totTalVal.value = 0.01;
        playLengths.value = 0.0;
      }
    }

    // 如果有歌曲名，则直接设置状态，否则延迟设置状态
    final int playState = viewData['PlayState'];
    if (viewData.containsKey('TrackName') &&
        viewData['TrackName'].toString().isNotEmpty) {
      stateValid();
      _setPlayState(playState);
    } else {
      stateStep(() {
        _setPlayState(playState);
      });
    }
  }

  void _setPlayState(int status) {
    final bool playState = (status == 0 ? true : false);
    // Aux 和 BT 的切换，暂时不处理
    if (sourceInput.value == 'LINE-IN' && sourceInput.value == 'BT') return;
    isPlaying.value = playState;
  }

  void _setPlayBtnDisable(bool seekStatus, bool nextStatus, bool prevStatus) {
    seek.value = seekStatus;
    nextBtn.value = nextStatus;
    prevBtn.value = prevStatus;
  }

  void _setPlayInfoData(Map viewData) {
    // 当没有数据的时候，清空数据
    final String trackName = viewData['TrackName'] ?? '';
    if (trackName == '' || trackName == 'Not provided') {
      _clearPlayInfoData();
      return;
    }

    songName.value = viewData['TrackName'];
    if (songName.value.contains('%')) {
      songName.value = Uri.decodeFull(songName.value);
    }
    songArtist.value = viewData['Artist'];
    if (songArtist.value.contains('%')) {
      songArtist.value = Uri.decodeFull(songArtist.value);
    }
    songAlbum.value = viewData['Album'];
    sourceCurrent.value = viewData['Current Source'].toString();

    // 设置图片
    if (viewData.containsKey('CoverArtUrl')) {
      _setPlayImage(viewData['CoverArtUrl']);
    }
    //
  }

  void _setPlayImage(String url) {
    if (!url.contains('http')) {
      if (sourceCurrent.value == '22') {
        songImage.value = '';
        TakeBgColor.getImageDominantColor(songImage.value, reOld: true);
        return;
      }

      if (url == 'coverart.jpg' || url == 'defaultArt.jpg') {
        final urlData =
            'http://$ip/$url?Album=${songAlbum.replaceAll(RegExp(r'\s+'), '')}';
        setSongImage(urlData);
        return;
      }
    } else {
      setSongImage(url);
    }
  }

  void _setPodCastStatus(String data) {
    if (data.contains('spotify:episode:')) {
      spotifyPodCast.value = true;
    } else {
      spotifyPodCast.value = false;
    }
  }

  void _clearPlayInfoData() {
    // if (item['TotalTime'] > 0) return;
    // if (item['Album'] != '') return;
    // if (item['Artist'] != '') return;
    // if (item['CoverArtUrl'] != '') return;
    TakeBgColor.getImageDominantColor(songImage.value, reOld: true);
    sourceCurrent.value = '0';
    songName.value = '';
    songArtist.value = '';
    songAlbum.value = '';
    songImage.value = '';
    totTalVal.value = 0.1;
    playLengths.value = 0.0;
    mime.value = '';
    sampleRate.value = '';
  }

  // 在刷新USB列表的时候卡住状态
  bool loadUsbPagesLock = false;

  void setBrowseView(viewData) async {
    // Browser视图
    if (viewData['Browser'] == 'HOME') {
      //
      final itemCount = viewData['Item Count'];
      if (itemCount <= 0) return;

      // list
      final itemList = viewData['ItemList'];
      for (var el in itemList) {
        if (el['Name'] == 'USB') {
          int fileId = el['Item ID'];
          selectItem(fileId);
        }
      }
    }

    // USB视图
    if (viewData['Browser'] == 'USB') {
      int pageNo = viewData['PageNo'];
      int totalItems = viewData['TotalItems'];

      Log.d('当前在第 $pageNo 页');
      usbPageIndex.value = pageNo;
      usbTotalSongs.value = totalItems;

      if (pageNo == 1) {
        if (totalItems != 0) {
          int allPages = (totalItems / 20).ceil();
          usbPages = allPages;
          Log.d('当前总共有 $totalItems 首歌曲 -- 总共有 $allPages 页');
          // 锁住状态 代表在加载
          loadUsbPagesLock = true;
        }
      }

      usbSongList.clear();

      final itemList = viewData['ItemList'];
      final List<Map<String, String>> usbList = [];
      for (var el in itemList) {
        final Map<String, String> item = {
          'name': '${el['Name']}',
          'id': '${el['Item ID']}',
          'page': '$pageNo',
        };
        usbList.add(item);
      }

      if (usbList.isNotEmpty) {
        usbSongList.value = usbList;
      } else {
        usbSongList.clear();
      }
    }
  }

  final Rx<BT_STATUE> btsState = Rx<BT_STATUE>(BT_STATUE.values[0]);
}

enum BT_STATUE {
  TL_BT_STATE_UNKNOWN,
  TL_BT_STATE_WAIT, // wait device
  TL_BT_STATE_SHUTDOWN,
  TL_BT_STATE_POWERON,
  TL_BT_STATE_STANDBY,
  TL_BT_STATE_SCANNING,
  TL_BT_STATE_PAIRING,
  TL_BT_STATE_RECONNECTING,
  TL_BT_STATE_CONNECTED,
  TL_BT_STATE_PLAYING,
  TL_BT_STATE_BROADCAST_RECEIVING,
  TL_BT_STATE_BROADCAST_SENDING,
}
