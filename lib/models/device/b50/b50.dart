import 'dart:async';
import 'dart:convert';
import 'package:gbk_codec/gbk_codec.dart';
import 'package:get/get.dart';
import 'package:gocontrol/common/ble_msg.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/common/toast.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_ble.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/models/model_class/a3_services.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';

class B50 extends AbstractBLE with BaseAudio, BLEandOnServices {

  B50(ScanResult scanResult) { 
    device = scanResult.device;
    rssi = scanResult.rssi;
  }

  @override 
  Origins origin = Origins.ble;

  @override 
  RxBool isBLEConnected = false.obs;

  RxBool connectState = false.obs;

  @override
  RxBool reconnecting = false.obs;

  int rssi = 0;

  // 
  StreamSubscription? listenBleState;
  StreamSubscription? listenNotify;

  // 设备名字
  @override 
  RxString name = ''.obs;
  // 固件版本
  @override 
  RxString version = ''.obs;
  // 设备音量
  @override
  RxDouble volume = 0.0.obs;
  // LED灯显示
  @override 
  RxBool led = false.obs;
  // 设备音量步进长度
  @override 
  RxInt vst = 3.obs;
  @override
  RxBool updateVer = false.obs;
  // 唱片机模式 MM MC
  RxBool phonoMode = false.obs;
  // 跟随电视关机
  RxBool arcMode = false.obs;
  // 是否关闭外放
  RxBool tlmMode = false.obs;
  @override
  RxString updateStr = ''.obs;
  // 
  @override
  RxList<Map> dataList = RxList<Map>([]);
  @override
  RxInt usbPlayIndex = 0.obs;
  @override
  RxInt dataTot = 0.obs;
  @override
  RxInt dataKey = 0.obs;

  @override
  Future<void> getSongListForUSB() async {
    if (dataTot.value == 0) return;
    String remoteId = device.remoteId.toString();
    String storageKey = '$remoteId:USB:SONGLIST';
    Log.d(StorageClass.getStorage(storageKey));
    if (StorageClass.getStorage(storageKey) != null) {
      final songList = json.decode(StorageClass.getStorage(storageKey));
      if (songList.length == dataTot.value) {
        // Log.d('同一首');
        RxList<Map> newList = RxList<Map>([]);
        for (var el in songList) {
          newList.add({
            'name': '${el['name']}'.obs,
            'id': el['id'],
          });
        }
        dataList.value = newList;
        sendMsgToBLE(msg:'DSK:CUR');
        return;
      } else {
        Log.d('不同一首');
      }
    }
    const snum = 15;
    final startKey = dataKey.value;
    final endKey = (startKey + snum >= dataTot.value) ? dataTot.value : startKey + snum;

    for (int keyNum = startKey; keyNum < endKey; keyNum++) {
      final item = {
        'id': keyNum,
        'name': ''.obs
      };
      dataList.add(item);
      // 发送BLE请求并等待响应
      sendMsgToBLE(msg: 'DSK:LST:$keyNum:${keyNum + 1};');
      await Future.delayed(const Duration(milliseconds: 88));
      // 确保数据已被接收，如果不是，则重发请求
      while (dataList[keyNum]['name'].value == '') {
        if (isBLEConnected.value == false) break;
        sendMsgToBLE(msg: 'DSK:LST:$keyNum:${keyNum + 1};');
        await Future.delayed(const Duration(milliseconds: 88));
      }
    }
    // 更新dataKey的值
    dataKey.value = endKey;
  }

  @override
  void clearAllUSBinfo(){
    dataList.clear();
    usbPlayIndex.value = 0;
    dataTot.value = 0;
    dataKey.value = 0;
  }
  
  @override
  void updateConfirm(){
    sendMsgToBLE(msg: 'UPG:CONFIRM;');
  }

  @override
  void getVersion() => sendMsgToBLE(msg: 'VER;');

  // 步进长度增加
  @override
  void vstAdd(){
    sendMsgToBLE(msg: 'VST:${vst.value + 1};');
  }
  // 步进长度减少
  @override
  void vstSub(){
    sendMsgToBLE(msg: 'VST:${vst.value - 1};');
  }
  
  // 设备输入源
  @override
  RxString sourceInput = ''.obs;
  // 要更换的输入源 
  @override
  RxString newSourceInput = ''.obs;
  // 输入源列表 
  @override
  final RxList<Map> sourceInputList = RxList<Map>([]);
  // 是否正在切换输入源
  @override
  RxBool swingSRC = false.obs;

  // 重连BLE
  @override
  Future<void> reconnectBLE() async {
    if (isBLEConnected.value) return Log.e('已连接，请勿重复连接');
    if (reconnecting.value) return;
      // disconnectBLE();
      // reconnecting.value = false;
      // await Future.delayed(const Duration(milliseconds: 300));
    try{
      reconnecting.value = true;
      // 重新发起连接
      await device.connect().timeout(
        const Duration(seconds: 15), 
        onTimeout:(){
          reconnecting.value = false;
          disconnectBLE();
        }
      );
      // 如果连接上的时候不是本设备就断开
      if (homCon.selectDevice != this) {
        disconnectBLE();
        reconnecting.value = false;
        return;
      }
      // 监听状态
      listenBLEStateChange();
      // 
      await onServices();
      listenNotify = notify!.onValueReceived.listen((msg) {
        try {
          BleMsg.getMsg(ascii.decode(msg), this);
        } on FormatException {
          try {
            BleMsg.getMsg(utf8.decode(msg), this);
          } on FormatException {
            // 使用GBK编码
            String result = gbk_bytes.decode(msg);
            BleMsg.getMsg(result, this);
          }
        }
      });
      getDeviceName();
      await Future.delayed(const Duration(milliseconds: 200));
      reconnecting.value = false;
    } catch(err) {
      Log.e('$name notify不可用');
    }
  }

  @override
  Future<void> connectBLE() async {
    Log.d('发起连接');
    if (connectState.value) return Log.e('正在连接，勿重复连接');
    if (isBLEConnected.value) return Log.e('已连接，请勿重复连接');
    connectState.value = true;
    try {
      await device.connect();
      await onServices();
      listenBLEStateChange();
      listenNotify = notify!.onValueReceived.listen((msg) {
        try {
          BleMsg.getMsg(ascii.decode(msg),this);
        } on FormatException {
          try {
            BleMsg.getMsg(utf8.decode(msg),this);
          } on FormatException {
            String result = gbk_bytes.decode(msg);
            BleMsg.getMsg(result,this);
          }
        }
      });
      getDeviceName();
      sendMsgToBLE(msg: 'BMO;');
      await Future.delayed(const Duration(milliseconds: 200));
      connectState.value = false;
      // await Future.delayed(const Duration(seconds: 10),(){
      //   if (name.value == '' && !isBLEConnected.value) {
      //     Log.e('断开因为连接状态和名字都没有');
      //     disconnectBLE(remove: true);
      //   }
      // });
    } catch(e) {
      Log.e('连接失败 $e');
      disconnectBLE(remove: true);
    }
  }

  Future<void> connectBLE2() async {
    if (isBLEConnected.value) return Log.e('已连接，请勿重复连接');
    if (connectState.value) return Log.e('正在连接，勿重复连接');
    connectState.value = true;
    try {
      await device.connect();
      await onServices();
      listenBLEStateChange();
      listenNotify = notify!.onValueReceived.listen((msg) {
        try {
          BleMsg.getMsg(ascii.decode(msg),this);
        } on FormatException {
          try {
            BleMsg.getMsg(utf8.decode(msg),this);
          } on FormatException {
            String result = gbk_bytes.decode(msg);
            BleMsg.getMsg(result,this);
          }
        }
      });
      getDeviceName();
      sendMsgToBLE(msg: 'BMO;');
      await Future.delayed(const Duration(milliseconds: 200));
      connectState.value = false;
      await Future.delayed(const Duration(milliseconds: 600));
    } catch(e) {
      Log.e('onServices 失败 $e');
      disconnectBLE(remove: true);
    }
  }

  // 断开BLE
  @override
  Future<void> disconnectBLE({
    bool remove = false
  }) async {
    if (remove && homCon.adevices.contains(this)) homCon.adevices.remove(this);
    btsPlay.value = false;
    try {
      Log.e('连接断开 ${device.platformName} remove = $remove');
      await device.disconnect();
      if (listenNotify != null) {
        listenNotify!.cancel();
        listenNotify = null;
      }
      if (listenBleState != null) {
        listenBleState!.cancel();
        listenBleState = null;
      }
    } catch (e) {
      Log.e('连接已断开 $e');
    }
  }

  // 监听设备情况
  @override
  void listenBLEStateChange() {
    listenBleState = device.connectionState.listen((event) async {
      if (event == BluetoothConnectionState.connected) {
        isBLEConnected.value = true;
      } else if (event == BluetoothConnectionState.disconnected) {
        isBLEConnected.value = false;
        AppToast.show('${name.value} xxx has disconnected!');
        Log.e('${name.value} xxx has disconnected!');
        // if (homCon.selectDevice == this) {
          // if (Get.currentRoute == '/home' || Get.currentRoute == '/') return;
          // Get.offAllNamed('/home');
          // bool flag = Get.currentRoute != Routes.homePage;
          // if (flag) 
        // }
        if (listenNotify != null) {
          listenNotify!.cancel();
          listenNotify = null;
        }
        if (listenBleState != null) {
          listenBleState!.cancel();
          listenBleState = null;
        }
      }
    });
  }

  // 发送消息
  @override
  void sendMsgToBLE({String? msg, List<int>? byteMsg}) async {
    try {
      if (!device.isConnected || !isBLEConnected.value) {
        Log.d('[${device.platformName}] 设备未连接，无法发送消息 [$msg]');
        return;
      }
      if (write == null) return Log.d('[${device.platformName}] 无法发送消息 [$msg]，因为没有可写特征值');
      if (msg != null) await write!.write(ascii.encode(msg),withoutResponse: write!.properties.writeWithoutResponse);
      if (byteMsg != null) await write!.write(byteMsg,withoutResponse: write!.properties.writeWithoutResponse);
    } catch (e) {
      Log.e('${device.platformName} 写入数据失败: $e');
    }
  }

  // 初始化信息
  @override
  Future<void> initBLEInfo() async {
    await Future.delayed(const Duration(milliseconds: 300),()=> getVolume());
    await Future.delayed(const Duration(milliseconds: 0),()=> getSourceInput());
    if (sourceInputList.isEmpty) {
      await Future.delayed(const Duration(milliseconds: 300),()=> getSourceList());
    }
    await Future.delayed(const Duration(milliseconds: 150),()=> sendMsgToBLE(msg: 'BTS;'));
    await Future.delayed(const Duration(milliseconds: 0),()=> getBtm1());
    await Future.delayed(const Duration(milliseconds: 300),()=> getBtm2());
    await Future.delayed(const Duration(seconds: 2), () async {
      if (sourceInput.value == '') getSourceInput();
      if (sourceInputList.isEmpty) getSourceList();
    });
   
  }

  // 初始化Audio界面信息
  @override
  void initAudioData() async {
    try {
      await Future.delayed(const Duration(milliseconds: 0),()=> getTreble());
      await Future.delayed(const Duration(milliseconds: 200),()=> getMid());
      await Future.delayed(const Duration(milliseconds: 0),()=> getBass());
      await Future.delayed(const Duration(milliseconds: 200),()=> getBalance());
      await Future.delayed(const Duration(milliseconds: 0),()=> getMaxVolume());
      await Future.delayed(const Duration(milliseconds: 200),()=> getCrossover());
      await Future.delayed(const Duration(milliseconds: 0),()=> getFrequency());
      await Future.delayed(const Duration(milliseconds: 200),()=> getDeepBass());
      await Future.delayed(const Duration(milliseconds: 0),()=> getIntensity());
      await Future.delayed(const Duration(milliseconds: 200),()=> getDeepBass());
      await Future.delayed(const Duration(milliseconds: 0),()=> getEqIndex());
      await Future.delayed(const Duration(milliseconds: 200),()=> getEqList());
      await Future.delayed(const Duration(milliseconds: 0),()=> getCustomEQList());
    } catch (e) {
      Log.e('连接断开');
    } 
  }

  // 获取设备名字
  @override
  void getDeviceName() => sendMsgToBLE(msg: 'NAM;');

  // 设置设备名字
  @override
  void setDeviceName(String name) {
    try {
      Get.toNamed('/');
      sendMsgToBLE(msg: name);
      Future.delayed(const Duration(milliseconds: 500),()=> disconnectBLE());
    } catch (e) {
      Log.e('连接断开');
    }
  } 

  @override
  void getUpdateVer(){
    sendMsgToBLE(msg: 'UPG;');
  }

  // 获取设备音量 
  @override
  void getVolume() => sendMsgToBLE(msg: 'VOL;');
  // 设置设备音量
  @override
  void setVolume(double volume) => sendMsgToBLE(msg: 'VOL:${volume.round()};');
  
  // 获取输入源列表
  @override
  void getSourceList() => sendMsgToBLE(msg: 'LST;');
  
  // 获取当前输入源状态
  @override
  void getSourceInput() {
    sendMsgToBLE(msg: 'SRC;');
  }

  // 设置当前输入源
  @override
  void setSourceInput(String src) {
    if (sourceInput.value != 'AURA' && src == 'AURA') {
      if (auracastType.value == 2 || auracastType.value == 4) offAuracast();
    }
    sendMsgToBLE(msg: 'SRC:$src;');
  }
  // 上一曲
  @override
  void prevPlay() => sendMsgToBLE(msg: 'PRE;');
  // 下一曲
  @override
  void nextPlay() => sendMsgToBLE(msg: 'NXT;');
  // 播放和暂停
  @override
  void playAndPauseSong() {
    sendMsgToBLE(msg: 'POP;');
  }

  // USB列表随机播放
  final RxInt usbPlayIndexForRandom = 0.obs;
  
  void randomPlay() {
    if (usbPlayIndexForRandom.value == 0) {
      sendMsgToBLE(msg: 'DLM:1;');
    } else if (usbPlayIndexForRandom.value == 1) {
      sendMsgToBLE(msg: 'DLM:2;');
    } else if (usbPlayIndexForRandom.value == 2) {
      sendMsgToBLE(msg: 'DLM:0;');
    }
  }

  // ************* ble没有此操作
    @override
    void playSong() {}
    @override
    void pauseSong() {}
  // *************

  // 重置设备
  @override
  void resetDevice(){
    sendMsgToBLE(msg: 'SYS:RESET;');
  }
  
  
  // Audio sound Eq部分
  @override
  final RxDouble treble = 0.0.obs;

  @override
  void getTreble() => sendMsgToBLE(msg: 'TRE;');

  @override
  void setTreble(double val) => sendMsgToBLE(msg: 'TRE:${val.ceil()};');

  @override 
  final RxDouble mid = 0.0.obs;
  
  @override 
  void getMid()=> sendMsgToBLE(msg: 'MID;');

  @override
  void setMid(double val) => sendMsgToBLE(msg: 'MID:${val.ceil()};');

  @override
  final RxDouble bass = 0.0.obs;

  @override
  void getBass() => sendMsgToBLE(msg: 'BAS;');

  @override
  void setBass(double val) => sendMsgToBLE(msg: 'BAS:${val.ceil()};');

  @override
  final RxDouble balance = 0.0.obs;

  @override
  void getBalance() => sendMsgToBLE(msg: 'BAL;');

  @override
  void setBalance(double val) => sendMsgToBLE(msg: 'BAL:${val.ceil()};');

  @override
  final RxDouble maxVolume = 30.0.obs;

  @override
  void getMaxVolume() => sendMsgToBLE(msg: 'MXV;');

  @override
  void setMaxVolume(double val) => sendMsgToBLE(msg: 'MXV:${val.ceil()};');

  @override 
  final RxBool crossover = false.obs;

  @override
  void getCrossover() => sendMsgToBLE(msg: 'CFE;');

  @override
  void setCrossover(bool val){
    if(val){
      sendMsgToBLE(msg: 'CFE:1;');
    }else{
      sendMsgToBLE(msg: 'CFE:0;');
    }
  }

  @override
  RxDouble frequency = 50.0.obs;

  @override
  void getFrequency(){
    sendMsgToBLE(msg: 'CFF;');
  }

  @override
  void setFrequency(double val){
    sendMsgToBLE(msg: 'CFF:${val.ceil()};');
  }

  @override
  RxBool deepBass = false.obs;

  @override
  void getDeepBass(){
    sendMsgToBLE(msg: 'VBS;');
  }

  @override
  void setDeepBass(bool val){
    if(val){
      sendMsgToBLE(msg: 'VBS:1;');
    }else{
      sendMsgToBLE(msg: 'VBS:0;');
    }
  }

   @override
  RxDouble intensity = 0.0.obs;
  
  @override
  void getIntensity(){
    sendMsgToBLE(msg: 'VBI;');
  }

  @override
  void setIntensity(double val){
    sendMsgToBLE(msg: 'VBI:${val.ceil()};');
  }


  @override
  RxInt eqIndex = 0.obs;

  @override
  RxList<Map> eqList = RxList<Map>([]);

  @override
  void setEqIndex(int index){
    sendMsgToBLE(msg: 'EQS:$index;');
  }

  @override
  void getEqIndex(){
    sendMsgToBLE(msg: 'EQS;');
  }

  @override
  void getEqList(){
    sendMsgToBLE(msg: 'PEQ;');
  }

  @override
  void setEqList(String str,{bool?custom}){
    List list = str.split(',');
    if(custom == null){
      eqList.clear();
      for(String item in list){
        eqList.add({
          'id': int.parse(item.substring(0,item.indexOf('@'))),
          'name': item.substring(item.indexOf('@') + 1,item.length),
        });
      }
      Log.p('当前得到的预设列表 -- $eqList');
    }
  }

  final RxString deviceMode = 'B50'.obs;

  final RxMap btm1 = RxMap({
    'sink': '',
    'name': '',
    'codec': '',
    'state': ''
  });

  final RxMap btm2 = RxMap({
    'sink': '',
    'name': '',
    'codec': '',
    'state': ''
  });

  final RxBool showBtm1 = false.obs;
  final RxBool showBtm2 = false.obs;
  final RxBool btsPlay = false.obs;

  void getBtm1() => sendMsgToBLE(msg: 'BTM:0;');
  void getBtm2() => sendMsgToBLE(msg: 'BTM:1;');

  // 自定义eq
  @override
  RxList<Map> customEQList = RxList<Map>([]);

  @override
  final customEQindex = RxInt(-1);

  @override
  List<Map> customEQValueList = [
    {
      "freq": '125',
      "gain": RxDouble(0)
    },
    {
      "freq": '250',
      "gain": RxDouble(0)
    },
    {
      "freq": '500',
      "gain": RxDouble(0)
    },
    {
      "freq": '1000',
      "gain": RxDouble(0)
    },
    {
      "freq": '2000',
      "gain": RxDouble(0)
    },
    {
      "freq": '4000',
      "gain": RxDouble(0)
    },
    {
      "freq": '8000',
      "gain": RxDouble(0)
    },
    {
      "freq": '16000',
      "gain": RxDouble(0)
    }
  ];

  @override
  void getCustomEQList() {
    sendMsgToBLE(msg: 'CEQ:LST;');
  }

  @override
  void customEQListValReset() {
    for (var el in customEQValueList) {
      el['gain'].value = 0.0;
    }
  }
  
  @override
  void getCEQandFLT(String val) {
    sendMsgToBLE(msg: '$val;');
  }

  @override
  void initCustomEQval() async {
    for (int i = 0; i < customEQValueList.length; i++) {
      await Future.delayed(const Duration(milliseconds: 100),(){
        sendMsgToBLE(msg: 'CEQ:FLT:$i;');
      });
    }
  }

  @override
  void setFLT(String val) {
    sendMsgToBLE(msg: '$val;');
  }

  @override
  void saveCustomEQ() {
    int id = eqIndex.value - 10;
    for (var el in customEQList) {
      if (el['id'] == '$id') {
        Log.d(el['name']);
        String val = 'CEQ:SAV:$id:${el['name']};'; 
        sendMsgToBLE(msg: '$val;');
      }
    }
  }

  @override
  void delectCustomEQ() {
    try {
      int id = eqIndex.value - 10;
      String val = 'CEQ:DEL:$id';
      setEqIndex(0);
      sendMsgToBLE(msg: '$val;');
      Future.delayed(const Duration(milliseconds: 180),()=> getCustomEQList());
    } catch (e) {
      Log.e('连接断开');
    }
  }

  @override
  void clearCustomEQval() {
    for (var i = 0; i < customEQValueList.length; i++) {
      customEQValueList[i]['gain'].value = 0.0;
    }
  }

  RxBool auracastChild = false.obs;
  RxInt auracastType = 0.obs;
  RxString auraCastMastName = ''.obs;
  RxList<B50> childForAuracast = RxList<B50>([]);

  Future<void> auracastAddToPublic(RxList<B50> list) async {
    if (list.isEmpty) return;
    sendMsgToBLE(msg: 'AST:2;');
    for (B50 el in list) {
      await el.connectBLE2();
      if (el.isBLEConnected.value) {
        el.auracastChild.value = true;
        el.sendMsgToBLE(msg: 'SRC:AURA;');
        if (!childForAuracast.contains(el)) childForAuracast.add(el);
      }
    }
    toAuraCastSave();
  }

  Future<void> auracastAddToPrivate(RxList<B50> list) async {
    if (list.isEmpty) return;
    sendMsgToBLE(msg: 'AMN;');
    if (auraCastMastName.value == '') {
      await Future.delayed(const Duration(milliseconds: 100));
      sendMsgToBLE(msg: 'AMN;');
    }
    if (auraCastMastName.value == '') {
      await Future.delayed(const Duration(milliseconds: 100));
      sendMsgToBLE(msg: 'AMN;');
    }
    if (auraCastMastName.value == '') return;
    await Future.delayed(const Duration(milliseconds: 200));
    sendMsgToBLE(msg: 'AST:4;');
    await Future.delayed(const Duration(milliseconds: 50));
    for (B50 el in list) {
      await el.connectBLE2();
      if (el.isBLEConnected.value) {
        Log.d('连接成功!!!!!!!');
        el.auracastChild.value = true;
        el.sendMsgToBLE(msg: 'ASN:$auraCastMastName;');
        await Future.delayed(const Duration(milliseconds: 200));
        el.sendMsgToBLE(msg: 'SRC:AURA;');
        if (!childForAuracast.contains(el)) childForAuracast.add(el);
      }
    }
    toAuraCastSave();
  }
  
  void toAuraCastSave() {
    var remoteId = device.remoteId.toString();
    Map item = {
      'remoteId': remoteId,
      'mastName': auraCastMastName.value,
      'type': auracastType.value,
      'child': childForAuracast.map((e) => e.device.remoteId.toString()).toList()
    };
    StorageClass.setStorage('Auracast:Master', json.encode(item));
    homCon.masterId = item['remoteId'];
    homCon.auracastChildList = item['child'];
  }

  Future<void> offAuracast() async {
    sendMsgToBLE(msg: 'AST:0;');
    for (var el in childForAuracast) {
      el.sendMsgToBLE(msg: 'ASN:;');
      await Future.delayed(const Duration(milliseconds: 200));
      el.auracastChild.value = false;
      el.sendMsgToBLE(msg: 'SRC:BT;');
      await Future.delayed(const Duration(milliseconds: 50));
      el.disconnectBLE();
    }
    StorageClass.setStorage('Auracast:Master', null);
    homCon.masterId = '';
    homCon.auracastChildList.clear();
    childForAuracast.clear();
  }

}
