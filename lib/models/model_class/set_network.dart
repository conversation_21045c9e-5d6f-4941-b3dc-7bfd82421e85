import 'package:get/get.dart';

mixin SetDeviceNetWork {

  // 是否处于配网状态
  RxBool get wifiSetupState;
  // 当前配网的步骤
  RxInt get wifiSetupIndex;
  // 当前配网的信息的长度，用来拼接的数据的时候对接长度
  RxInt get wifiSetMsgLength;
  // 当前配网信息的数据内容
  RxList<int> get wifiSetMsg;
  // 当前配网缓环节搜索到的wifi
  RxList get wifiScanList;
  // 当前配网过程选择的wifi
  RxMap get selectWiFi;
  // 当前是否正在连接wifi
  RxBool get wifiConnectState;
  // WIFI连接结果如何
  RxInt get wifiConnectResult;
  
  void connectDevice();

  void connectToSlecetWiFi({
    required String ssid,
    required String pwd
  });

  void refreshWiFi();

  // @override // 发送配网1001信息
  // void sendSetup1001() {
  //   sendMsgToBLE2(byteMsg: [0x50,0x4c,0x01,0x10,0x00,0x00]);
  //   Log.o('0x1001');
  //   Future.delayed(const Duration(seconds: 12),() {
  //     if (wifiScanList.isEmpty) {
  //       beginWiFiSetup.value = 1;
  //       sendMsgToBLE2(byteMsg: [0x50,0x4c,0x01,0x10,0x00,0x00]);
  //       Future.delayed(const Duration(seconds: 12),() {
  //         AppToast.show('连接超时');
  //         disconnectBLE();
  //         homCon.setNetworkDeviceList.clear();
  //         Get.back();
  //       });
  //     }
  //   });
  // }

  // @override // 发送配网1002信息
  // void sendSetup1002(){
  //   beginWiFiSetup.value = 2;
  //   sendMsgToBLE2(byteMsg: [0x50,0x4c,0x07,0x10,0x00,0x00]);
  // }

  // @override // 发送配网1003信息
  // Future<void> sendSetup1003(String msg) async {
  //   connectWiFiState.value = true;
  //   beginWiFiSetup.value = 3;
  //   List<int> tag = [0x50, 0x4c];
  //   // 1002
  //   List<int> com = [0x02, 0x10];
  //   // 1003
  //   List<int> comCode = [0x03, 0x10];
  //   String code = '{"code":"CN"}';
  //   List<int> lenCode = Comm.calculateLengthBytes(code);
  //   List<int> datCode = Comm.convertStringToBytes(code);
  //   sendMsgToBLE2(byteMsg: tag+comCode+lenCode+datCode);

  //   await Future.delayed(const Duration(milliseconds: 500),() async {
  //     List<int> len = Comm.calculateLengthBytes(msg);
  //     List<int> dat = Comm.convertStringToBytes(msg);

  //     await Future.delayed(const Duration(milliseconds: 300),() {
  //       sendMsgToBLE2(byteMsg: tag+com+len+dat);
  //     });
  //     await Future.delayed(const Duration(seconds: 60),() { // 超时
  //       if (connectWiFiResult.value == 0) {
  //         connectWiFiState.value = false;
  //         selectWiFiInfo.value = {};
  //         beginWiFiSetup.value = 1;
  //         AppToast.show('Error Password');
  //       }
  //     });
  //   });
  // }

}