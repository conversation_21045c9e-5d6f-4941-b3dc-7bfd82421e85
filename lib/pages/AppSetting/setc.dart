
import 'package:get/get.dart';
import 'package:gocontrol/log.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppController extends GetxController {
  AppController() {
    () async {
      await initVersion();
    }();
  }
  
  final RxString _version = ''.obs;
  
  Future<void> initVersion() async {
    final info = await PackageInfo.fromPlatform();
    Log.e(info);
    _version.value = info.version;
  }
  
  String get version => _version.value;
}