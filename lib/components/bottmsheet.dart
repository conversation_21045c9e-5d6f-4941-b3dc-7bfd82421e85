import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/components/ontaps.dart';
import 'package:gocontrol/theme/theme.dart';

showMyBottomSheet({
  required double height,
  required String title,
  required Widget content,
  required RxBool showCancel,
  Function? cancel,
  Function? confirm,
  String? cancelText,
  String? confirmText,
  RxBool? confirmFuture,
  RxBool? changeOnlyConfirm,
  bool isDismissible = true
}){

  Widget getConfirm(){
    if(confirmFuture != null && confirmFuture.value){
      return Container(
        height: 42.sp,
        width: 42.sp,
        padding: EdgeInsets.all(10.sp),
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(themeBase.textColor1.value),
          strokeWidth: 2.6.sp,
        ),
      );
    }
    return AText(
      text: confirmText ?? 'confirm'.tr,
      size: themeBase.buttonFont.value,
      color: themeBase.textColor1.value,
    );
  }

  bool isShowCancel(){
    if(changeOnlyConfirm != null) return !changeOnlyConfirm.value;
    return showCancel.value;
  }
  
  double bottomPadding;
  if (themeBase.bottomPadding.value > 0) {
    bottomPadding = themeBase.bottomPadding.value;
  } else {
    bottomPadding = 10.sp;
  }

  Get.bottomSheet(
    isDismissible: isDismissible,
    enterBottomSheetDuration: const Duration(milliseconds: 200),
    exitBottomSheetDuration: const Duration(milliseconds: 160),
    SingleChildScrollView(
      physics: const NeverScrollableScrollPhysics(),
      child: SafeArea(
        top: false,
        bottom: false,
        child: Container(
          height: height,
          margin: EdgeInsets.only(bottom: cancel == null && confirm == null? 10.sp : bottomPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: SizedBox(
                  child: Column(
                    children: [
                      Container(
                        alignment: Alignment.center,
                        height: 55.sp,
                        margin: EdgeInsets.only(top: 5.sp),
                        child: Obx(() => AText(
                          text: title,
                          size: themeBase.subHeadingFont.value + 1.sp,
                          color: themeBase.primaryColor.value,
                        )),
                      ),
                      // 中间内容
                      Flexible(child: content)
                    ],
                  ),
                ),
              ),
              Visibility(
                visible: cancel != null || confirm != null,
                child: Container(
                  height: 42.sp,
                  margin: EdgeInsets.symmetric(
                    horizontal: 14.sp
                  ),
                  child: Row(
                    children: [
                      Obx(() => Visibility(
                        visible: isShowCancel(),
                        child: Flexible(
                          child: OnTapScaleToSmallBox(
                            onTap: ()=> cancel!(),
                            child: Container(
                              height: 42.sp,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: themeBase.buttonColor2.value,
                                borderRadius: BorderRadius.circular(10.r)
                              ),
                              child: AText(
                                text: cancelText ?? 'cancel'.tr,
                                size: themeBase.buttonFont.value,
                                color: themeBase.textColor1.value,
                              ),
                            ),
                          )
                        ),
                      )),
                      Obx(() => Visibility(
                        visible: isShowCancel(),
                        child: SizedBox(width: 14.sp),
                      )),
                      Flexible(
                        child: OnTapScaleToSmallBox(
                          onTap: () async {
                            if(confirmFuture != null){
                              if(confirmFuture.value) return;
                              confirmFuture.value = true;
                              if(confirm != null) await confirm();
                              confirmFuture.value = false;
                            }else{
                              if(confirm != null) confirm();
                            }
                          },
                          child: Container(
                            height: 42.sp,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: themeBase.buttonColor1.value,
                              borderRadius: BorderRadius.circular(10.r)
                            ),
                            child: Obx(() => getConfirm())
                          ),
                        )
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ),
    backgroundColor: themeBase.textColor1.value
  );
}