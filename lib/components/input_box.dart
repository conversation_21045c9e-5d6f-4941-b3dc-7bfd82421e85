import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/theme/theme.dart';

class InputBox extends StatelessWidget {
  const InputBox({
    super.key,
    required this.height,
    required this.focusNode,
    required this.textController,
    required this.textColor,
    this.space,
    this.leftIcon,
    this.rightIcon,
    this.borderRadius,
    this.autofocus = false,
    this.inputType,
    this.display,
    this.color,
    this.placeholder,
    this.change,
    this.errorBorder,
    this.textSize,
    this.margin
  });

  final Widget? leftIcon;
  final Widget? rightIcon;
  final double height;
  final double? space;
  final double? borderRadius;
  final bool autofocus;
  final FocusNode focusNode;
  final TextEditingController textController;
  final TextInputType? inputType;
  /// 是否隐藏输入内容
  final RxBool? display;
  final Color? color;
  final Color textColor;
  final String? placeholder;
  final Function? change;
  final RxBool? errorBorder;
  final double? textSize;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
      alignment: Alignment.center,
      height: height - 2.sp,
      margin: margin,
      padding: EdgeInsets.only(
        left: space??8.sp,
        right: space??8.sp,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius??10.sp),
        color: color??themeBase.inputColor.value,
        border: Border.all(
          color: showErrorBorder()?themeBase.errorColor.value:Colors.transparent,
          width: 1.sp
        ),
      ),
      child: Row(
        children: [
          Padding(
            padding: EdgeInsets.only(right: space??8.sp),
            child: leftIcon??const SizedBox()
          ),
          Flexible(
            child: TextField(
              cursorColor: themeBase.primaryColor.value,
              autofocus: autofocus,
              focusNode: focusNode,
              controller: textController,
              onTapOutside: (e) => focusNode.unfocus(),
              keyboardType: inputType,
              obscureText: display != null?!(display!.value):false,
              decoration: InputDecoration(
                hintText: placeholder,
                hintStyle: TextStyle(
                  color: textColor.withAlpha((255 * 0.3).toInt()),
                  fontSize: textSize ?? themeBase.inputFont.value,
                  fontFamily: 'Medium',
                ),
                contentPadding: EdgeInsets.all(0.sp),
                border: InputBorder.none,
                focusedBorder: unBorder(),
                enabledBorder: unBorder(),
              ),
              onChanged: (String value){
                if(change != null) change!(value);
              },
              style: TextStyle(
                color: textColor,
                fontSize: textSize ?? themeBase.inputFont.value,
                fontFamily: 'Medium',
                letterSpacing: 0.8.sp
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: space ?? 8.sp),
            child: rightIcon??const SizedBox()
          ),
        ],
      ),
    ));
  }

  OutlineInputBorder unBorder(){
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(10.sp),
      borderSide: BorderSide.none
    );
  }

   bool showErrorBorder(){
    if(errorBorder != null) return errorBorder!.value;
    return false;
  }
}