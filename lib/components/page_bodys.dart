import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gocontrol/theme/theme.dart';

/// 给当前页面设置模糊导航栏
class PagesBody extends StatelessWidget {
  const PagesBody({
    super.key,
    required this.body, 
    this.topbar,
    this.showTopBar = true,
    this.endDrawer,
    this.scroll = true,
    this.loadingBody,
    this.playbody = false
  });

  final BodyBox body;
  final Widget? topbar;
  final bool showTopBar;
  final Widget? endDrawer;
  final bool scroll;
  final Widget? loadingBody;
  final bool playbody;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: themeBase.bgColor.value,
      endDrawer: endDrawer, 
      body: CustomScrollView(
        physics: scroll? null : const NeverScrollableScrollPhysics(),
        slivers: [
          SliverAppBar(
            backgroundColor: Colors.transparent, // 设置为透明
            toolbarHeight: showTopBar? themeBase.topBarHeight : 0, // 是否显示TopBar
            automaticallyImplyLeading: false, // 隐藏左侧默认的返回按钮
            elevation: 0, // 取消阴影
            pinned: true, // 固定在顶部
            systemOverlayStyle: playbody? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark, // 导航栏颜色模式
            actions: [Container()], // 取消默认导航栏按钮
            flexibleSpace: showTopBar? FlexibleSpaceBar( 
              // 自定义导航栏头部
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // 其他背景元素，例如背景图片
                  topbar ?? Container(),
                ],
              ),
            ) : Container(),
          ),
          body,
          SliverToBoxAdapter(child: loadingBody ?? Container())
        ],
      ),
    );
  }
}

// 内容使用列表的时候一定要把 scrollBox 设置成true不然会报错
class BodyBox extends StatelessWidget {
  const BodyBox({
    super.key,
    required this.child,
    this.scrollBox = false,
    this.showTopBar = false,
  });

  final Widget child;
  final bool scrollBox;
  final bool showTopBar;

  @override
  Widget build(BuildContext context) {
    if(scrollBox){
      return child;
    }else{
      return SliverToBoxAdapter(
        child: SizedBox(
          height: showTopBar? Get.height - (themeBase.topBarHeight + themeBase.searHeight.value) : Get.height,
          child: child,
        ),
      );
    }
  }
}