import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class Loading extends StatelessWidget {
  const Loading({
    super.key,
    this.size,
    this.color,
    this.text
  });

  final double? size;
  final Color? color;
  final String? text;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        LoadingAnimationWidget.staggeredDotsWave(
          color: color ?? themeBase.primaryColor.value,
          size: size ?? 25.w,
        ),
        SizedBox(height: 12.sp),
        text != null? AText(
          text: text!,
          color: color ?? themeBase.primaryColor.value,
          size: themeBase.subBodyFont.value - 1.sp,
          family: TextFamily.medium,
        ) : const SizedBox(),
        // 
      ],
    );
  }
}