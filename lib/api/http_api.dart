import 'dart:io';
import 'dart:convert';
import 'package:gocontrol/log.dart';

class HttpApi {

  HttpApi(){
    httpClient = HttpClient()..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  }

  // 请求实例对象 
  late HttpClient httpClient;

  // 发起 HTTPS GET 请求
  Future<String> requestGet(String url) async {
    try {
      HttpClientRequest request = await httpClient.getUrl(Uri.parse(url));
      HttpClientResponse response = await request.close();
      String responseBody = await response.transform(utf8.decoder).join();
      if(response.statusCode == 200){
        return responseBody;
      }else{
        Log.e('requestGet 请求失败 ${response.statusCode}');
        return '';
      }
    } catch (e) {
      if ('$e'.contains('OS Error: Connection refused, errno = 61)')) return 'Error: Connection refused';
      Log.e('requestGet 请求捕捉到错误 $e');
      return '';
    }
  }

  // 转换 Url
  String getUri(String header,String ip,String command) {
    return '$header://$ip/httpapi.asp?command=$command';
  }

  Future<String> getStatusEx(String ip,{String header = 'http'}) async {
    final result = await requestGet(getUri(header, ip, 'getStatusEx'));
    return result;
  }

   // Future<String> request({required Uri uri}) async {
  //   try {
  //     final response = await http.get(uri)  ;
  //     if (response.statusCode == 200) {
  //       return response.body;
  //     } else {
  //       return '';
  //     }
  //   } catch(e) {
  //     Log.e('HttpApi请求错误: $e');
  //     return '';
  //   }
  // }

}
