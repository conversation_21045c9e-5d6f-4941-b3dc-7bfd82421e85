
class TcpApiAry {

  final String _header = 'MCU+PAS+RAKOIT';

  // 获取当前设备的版本
  String get versionGet => '$_header:VER&';

  // 设备名字
  String get deviceNameGet => '$_header:NAM&';
  // 设置设备名字
  String deviceNameSet(String name) {
    return '$_header:NAM:$name&';
  }

  // 获取音量
  String get volumeGet => 'MCU+VOL+GET';
  // 设置音量
  String volumeSet(int volume) {
    return 'MCU+VOL:$volume';
  }

  // 获取静音情况
  String get muteVolumeGet => '$_header:MUT&';
  // 设置静音情况
  String muteVolumeSet(int mute) {
    return '$_header:MUT:$mute&';
  }
  
  // 获取输入源列表
  String get sourceListGet => '$_header:LST&';
  // 当前设备的输入源
  String get sourceGet => '$_header:SRC&';
  // 设置输入源
  String sourceSet(String source) {
    return '$_header:SRC:$source&';
  }

  
}
