import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:gocontrol/common/libre_msg.dart';
import 'package:gocontrol/common/lock.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/device/libre/libre.dart';

class LuciApi {
  LuciApi();

  RawDatagramSocket? _rawSocket;
  RawDatagramSocket? _writeSocket;
  StreamSubscription? socketSubscription;
  StreamSubscription? writreSocketSubscription;
  
  final Set<String> _infoSet = {};
  final Lock _lock = Lock();
  String ipv4ip = '';

  // 固定端口
  static const int _lssdpPort = 1800;

  // * --- 以下是解析的数据格格式 ---
  // * [USN]: 指定LSx的MAC地址
  // * [Version]: 指定使用的LSSDP版本
  // * [FN]: 指定M-Search响应是否为请求的第一个通知
  // *     -1:第一次通知
  // *     —0:表示不是第一次通知
  // * [FWVERSION]: 指定LSx的固件版本
  // * [CAST_FWVERSION]: 指定在LSx上运行的Google Cast的版本
  // * [CAST_TIMEZONE]: 待实现
  // * [CAST_MODEL]: LSx设备型号
  // * [PORT]: 指定端口号
  // * [DeviceName]: LSx设备名
  // * [NETMODE]: LSx是否通过Wi-Fi或以太网连接到网络
  // * [SPEAKERTYPE]: LSx设备类型
  // * [TCPPORT]: 待实现
  // * [WIFIBAND]: LSx是否连接到2.4/5G以上的Wi-Fi。当LSx通过以太网连接到网络时不适用。
  // * [SOURCE_LIST]: 指定了LSx支持的音乐服务和来源
  // * --- 以下标头仅适用于MRA功能，不再适用 ---
  // * [State]: DeviceState头包含设备的当前状态:
  // *     -M:这个设备是Audio-Master
  // *     -F:设备处于空闲状态，不属于任何Zone
  // *     -S:它是ZoneID头中提到的区域的音频客户端
  // * [MRAMode]: 指定LSx的MRA模式

  // 
  void watch(void Function(List<String>) callback) async {

    await IpV4AndIpV6Tool.getIpv4AndIpV6Addresses((ipv4) => ipv4ip = ipv4);

    _rawSocket = await RawDatagramSocket.bind(InternetAddress.anyIPv4, _lssdpPort);

    socketSubscription = _rawSocket!.listen((event) {
      _callBackDeviceInfo(event, callback, _rawSocket!);
    });

    _rawSocket!.broadcastEnabled = true;
    try {
      _rawSocket!.joinMulticast(InternetAddress('***************'));
    } catch(e) {
      Log.e(e);
    }
  }
  
  void _callBackDeviceInfo(RawSocketEvent event, void Function(List<String>) callback, RawDatagramSocket socket) {
    if (event == RawSocketEvent.read) {
      final datagram = socket.receive();
      if (datagram == null) return;
      final String msg = utf8.decode(datagram.data).replaceAll('\r\n', ',');
      final String ip = datagram.address.address;
      final List<String> lt = msg.replaceAll('\r\n', ',').split(',')..removeAt(0);
      final List<String> mlt = ['ip:$ip',...lt];
      if (!_infoSet.contains(ip)) {
        _infoSet.add(ip);
        Log.r('接收到设备信息:$mlt');
        callback(mlt);
      }
    }
  }

  Future<void> startScan(void Function(List<String>) callback) async {
    await Future.delayed(const Duration(milliseconds: 50));
    
    if (_lock.isUsing) {
      await _lock.waitDone();
    }

    await _lock.mutex(() async {
      if (_writeSocket != null) {
        _writeSocket?.close();
        _writeSocket = null;
      }
      
      if (writreSocketSubscription != null) {
        await writreSocketSubscription?.cancel();
        writreSocketSubscription = null;
      }

      await Future.delayed(const Duration(seconds: 1));

      _writeSocket = await RawDatagramSocket.bind(InternetAddress.anyIPv4, 7771);
      List<int> data = utf8.encode('M-SEARCH * HTTP/1.1\r\nHOST: ***************:1800\r\n\r\nPROTOCOL: Version 1.0');
      InternetAddress multicastAddress = InternetAddress('***************');
      
      await Future.delayed(const Duration(milliseconds: 50));

      try {
        writreSocketSubscription = _writeSocket!.listen((event) {
          _callBackDeviceInfo(event, callback, _writeSocket!);
        });
      } catch (e) {
        Log.e(e);
      }

      _writeSocket!.send(data, multicastAddress, _lssdpPort);

      await Future.delayed(const Duration(seconds: 3));

      _writeSocket!.send(data, multicastAddress, _lssdpPort);

      await Future.delayed(const Duration(seconds: 3));

      _writeSocket!.send(data, multicastAddress, _lssdpPort);

      await Future.delayed(const Duration(seconds: 3));

      _writeSocket!.close();
      _infoSet.clear();
    });
  }

  List<int> lsxInfo({
    remoteId = 0,
    commandType = 0x01, // Message Box Type GET 1 / SET 2
    required command, // Message Box ID
    commandStatus = 0x00,
    required String data,
  }) {
    int dataLength = utf8.encode(data).length;
    int commandStatus = 0x00; 
    List<int> dataInit = data.codeUnits;

    int crc = calculateCRC16([
      remoteId & 0xFF, (remoteId >> 8) & 0xFF,
      commandType & 0xFF,
      command & 0xFF, (command >> 8) & 0xFF,
      commandStatus & 0xFF,
      dataLength & 0xFF,
      (dataLength >> 8) & 0xFF,
      ...dataInit
    ]);
    // 创建消息的字节列表
    List<int> messageBytes = [
      // 远程ID (16位)
      remoteId & 0xFF, (remoteId >> 8) & 0xFF,
      // 命令类型 (8位)
      commandType & 0xFF,
      // 命令 (16位)
      command & 0xFF, (command >> 8) & 0xFF,
      // 命令状态 (8位)
      commandStatus & 0xFF,
      // CRC (16位)
      crc & 0xFF, (crc >> 8) & 0xFF,
      // 数据长度 (16位)
      dataLength & 0xFF, (dataLength >> 8) & 0xFF,
      // 数据
      ...dataInit,
    ];
    return messageBytes;
  }

  int calculateCRC16(List<int> buf) {
    int counter;
    int crc = 0;
    for (counter = 0; counter < buf.length; counter++) {
      crc = (crc << 8) ^ crc16tab[((crc >> 8) ^ buf[counter]) & 0xFF];
    }
    return crc & 0xFFFF; // 确保结果为16位
  }
  
  final List<int> crc16tab = [
    0x0000,0x1021,0x2042,0x3063,0x4084,0x50a5,0x60c6,0x70e7,
    0x8108,0x9129,0xa14a,0xb16b,0xc18c,0xd1ad,0xe1ce,0xf1ef,
    0x1231,0x0210,0x3273,0x2252,0x52b5,0x4294,0x72f7,0x62d6,
    0x9339,0x8318,0xb37b,0xa35a,0xd3bd,0xc39c,0xf3ff,0xe3de,
    0x2462,0x3443,0x0420,0x1401,0x64e6,0x74c7,0x44a4,0x5485,
    0xa56a,0xb54b,0x8528,0x9509,0xe5ee,0xf5cf,0xc5ac,0xd58d,
    0x3653,0x2672,0x1611,0x0630,0x76d7,0x66f6,0x5695,0x46b4,
    0xb75b,0xa77a,0x9719,0x8738,0xf7df,0xe7fe,0xd79d,0xc7bc,
    0x48c4,0x58e5,0x6886,0x78a7,0x0840,0x1861,0x2802,0x3823,
    0xc9cc,0xd9ed,0xe98e,0xf9af,0x8948,0x9969,0xa90a,0xb92b,
    0x5af5,0x4ad4,0x7ab7,0x6a96,0x1a71,0x0a50,0x3a33,0x2a12,
    0xdbfd,0xcbdc,0xfbbf,0xeb9e,0x9b79,0x8b58,0xbb3b,0xab1a,
    0x6ca6,0x7c87,0x4ce4,0x5cc5,0x2c22,0x3c03,0x0c60,0x1c41,
    0xedae,0xfd8f,0xcdec,0xddcd,0xad2a,0xbd0b,0x8d68,0x9d49,
    0x7e97,0x6eb6,0x5ed5,0x4ef4,0x3e13,0x2e32,0x1e51,0x0e70,
    0xff9f,0xefbe,0xdfdd,0xcffc,0xbf1b,0xaf3a,0x9f59,0x8f78,
    0x9188,0x81a9,0xb1ca,0xa1eb,0xd10c,0xc12d,0xf14e,0xe16f,
    0x1080,0x00a1,0x30c2,0x20e3,0x5004,0x4025,0x7046,0x6067,
    0x83b9,0x9398,0xa3fb,0xb3da,0xc33d,0xd31c,0xe37f,0xf35e,
    0x02b1,0x1290,0x22f3,0x32d2,0x4235,0x5214,0x6277,0x7256,
    0xb5ea,0xa5cb,0x95a8,0x8589,0xf56e,0xe54f,0xd52c,0xc50d,
    0x34e2,0x24c3,0x14a0,0x0481,0x7466,0x6447,0x5424,0x4405,
    0xa7db,0xb7fa,0x8799,0x97b8,0xe75f,0xf77e,0xc71d,0xd73c,
    0x26d3,0x36f2,0x0691,0x16b0,0x6657,0x7676,0x4615,0x5634,
    0xd94c,0xc96d,0xf90e,0xe92f,0x99c8,0x89e9,0xb98a,0xa9ab,
    0x5844,0x4865,0x7806,0x6827,0x18c0,0x08e1,0x3882,0x28a3,
    0xcb7d,0xdb5c,0xeb3f,0xfb1e,0x8bf9,0x9bd8,0xabbb,0xbb9a,
    0x4a75,0x5a54,0x6a37,0x7a16,0x0af1,0x1ad0,0x2ab3,0x3a92,
    0xfd2e,0xed0f,0xdd6c,0xcd4d,0xbdaa,0xad8b,0x9de8,0x8dc9,
    0x7c26,0x6c07,0x5c64,0x4c45,0x3ca2,0x2c83,0x1ce0,0x0cc1,
    0xef1f,0xff3e,0xcf5d,0xdf7c,0xaf9b,0xbfba,0x8fd9,0x9ff8,
    0x6e17,0x7e36,0x4e55,0x5e74,0x2e93,0x3eb2,0x0ed1,0x1ef0
  ];
  
  Map<String, String> parseMessage(List<int> msg,Libre libre) {
    // 解析Remote ID
    List<int> remoteIdCode = msg.sublist(0, 2);
    int remoteId = (remoteIdCode[0] * 256) + remoteIdCode[1];

    // 解析Command Type
    List<int> commandTypeCode = msg.sublist(2, 3);
    int commandType = commandTypeCode[0];

    // 解析Command
    List<int> commandCode = msg.sublist(3, 5);
    int command = (commandCode[0] * 256) + commandCode[1];

    // 解析Command Status
    List<int> commandStatus = msg.sublist(5, 6);
    int commandStatusValue = commandStatus[0];

    // 解析CRC
    List<int> crcCode = msg.sublist(6, 8);  
    int crc = (crcCode[0] * 256) + crcCode[1];

    // 解析Data Length
    List<int> dataLengthCode = msg.sublist(8, 10);
    int dataLength = (dataLengthCode[0] * 256) + dataLengthCode[1];

    // 解析Data
    List<int> dataCode;

    if (dataLength == 0) {
      dataCode = [];
    } else {
      dataCode = msg.sublist(10,10 + dataLength);
    }

    String data;

    try { 
      data = utf8.decode(dataCode);
    } catch (e) {
      data = '';
      Log.e('错误信息无法处理 $msg');
    }

    if (10 + dataLength != msg.length) {
      List<int> newCode = msg.sublist(10 + dataLength);
      LibreMsg.getLuciMsg(newCode,libre);
    }

    return {
      'remoteId': '$remoteId',
      'commandType': '$commandType',
      'command': '$command',
      'commandStatus': '$commandStatusValue',
      'crc': '$crc',
      'dataLength': '$dataLength',
      'messageData': data.replaceAll(RegExp(r'\n'), ''),
    };
  }
}

class IpV4AndIpV6Tool {
  static Future<void> getIpv4AndIpV6Addresses(
    Function(String ipv4Address)didClickConfirmBtnAction
  ) async {
    String tempIpv4 = "";
    try {
      // 获取所有网络接口信息
      List<NetworkInterface> interfaces = await NetworkInterface.list(
        includeLoopback: true, // 是否包含回环接口
        includeLinkLocal: true, // 是否包含链路本地接口（例如IPv6的自动配置地址）。
        type: InternetAddressType.IPv4,
      );
      // 遍历所有网络接口
      for (var interface in interfaces) {
        // 遍历接口的地址
        for (var address in interface.addresses) {
          if (interface.name == 'en0' || interface.name == 'en1' || interface.name == 'en2') {
            if (address.address != '' || address.address.isNotEmpty) {
              tempIpv4 = address.address;
            }
          } else {
            continue;
          }
        }
      }
    } catch (e) {
      Log.d('Failed to get IP addresses: $e');
    } finally {
      didClickConfirmBtnAction(tempIpv4);
    }
  }
}

