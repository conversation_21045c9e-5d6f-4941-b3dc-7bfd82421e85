PODS:
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_blue_plus_darwin (0.0.2):
    - Flutter
    - FlutterMacOS
  - fluttertoast (0.0.2):
    - Flutter
  - get_settings (1.0.0):
    - Flutter
  - is_app_installed (0.0.1):
    - Flutter
  - MMKV (2.2.2):
    - MMKVCore (~> 2.2.2)
  - mmkv_ios (2.2.2):
    - Flutter
    - MMKV (< 2.3, >= 2.2.2)
  - MMKVCore (2.2.2)
  - network_info_plus (0.0.1):
    - Flutter
  - nsd_ios (0.0.1):
    - Flutter
  - on_audio_query_ios (0.0.1):
    - Flutter
    - SwiftyBeaver
  - open_settings_plus (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyBeaver (2.1.1)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_blue_plus_darwin (from `.symlinks/plugins/flutter_blue_plus_darwin/darwin`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - get_settings (from `.symlinks/plugins/get_settings/ios`)
  - is_app_installed (from `.symlinks/plugins/is_app_installed/ios`)
  - mmkv_ios (from `.symlinks/plugins/mmkv_ios/ios`)
  - network_info_plus (from `.symlinks/plugins/network_info_plus/ios`)
  - nsd_ios (from `.symlinks/plugins/nsd_ios/ios`)
  - on_audio_query_ios (from `.symlinks/plugins/on_audio_query_ios/ios`)
  - open_settings_plus (from `.symlinks/plugins/open_settings_plus/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - MMKV
    - MMKVCore
    - SwiftyBeaver

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_blue_plus_darwin:
    :path: ".symlinks/plugins/flutter_blue_plus_darwin/darwin"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  get_settings:
    :path: ".symlinks/plugins/get_settings/ios"
  is_app_installed:
    :path: ".symlinks/plugins/is_app_installed/ios"
  mmkv_ios:
    :path: ".symlinks/plugins/mmkv_ios/ios"
  network_info_plus:
    :path: ".symlinks/plugins/network_info_plus/ios"
  nsd_ios:
    :path: ".symlinks/plugins/nsd_ios/ios"
  on_audio_query_ios:
    :path: ".symlinks/plugins/on_audio_query_ios/ios"
  open_settings_plus:
    :path: ".symlinks/plugins/open_settings_plus/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_blue_plus_darwin: 904ed52825285a15f60e9d90312337a09f11a785
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  get_settings: 90c65efca34991969c01ed2b0473b04ac46f8d92
  is_app_installed: b309e0504aaf250c07042180710da422550c09a7
  MMKV: b4802ebd5a7c68fc0c4a5ccb4926fbdfb62d68e0
  mmkv_ios: 7c628eeef3886f9aa2224213a97b555076acb86b
  MMKVCore: a255341a3746955f50da2ad9121b18cb2b346e61
  network_info_plus: 6613d9d7cdeb0e6f366ed4dbe4b3c51c52d567a9
  nsd_ios: 8c37babdc6538e3350dbed3a52674d2edde98173
  on_audio_query_ios: e5afb0d4b42cfa3e8762d8ec1953540cb4f02100
  open_settings_plus: 4bd848866fea2becc124581d868a6164cf768831
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  SwiftyBeaver: ade157e4f857812e7d7f15f2e3396bb8733f8a1c
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: 487ffcd88c1ab152c9f5f69bac3288b2f42d7994

COCOAPODS: 1.15.2
