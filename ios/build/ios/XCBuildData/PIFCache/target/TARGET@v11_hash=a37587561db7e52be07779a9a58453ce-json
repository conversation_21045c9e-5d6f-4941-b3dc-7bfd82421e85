{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9822b3c6f21bbb5914e4ed6e5535614e03", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f292fa1a2e602e72cbae03ca4381d0ef", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9866068aadd32d93dae223922f3bbea3c0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981c2c7bb83ef85df2b5738875e12a3b9e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9866068aadd32d93dae223922f3bbea3c0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/develop/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802786e96202a989faf8737401d76d959", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fc062578f1be2e05ee972505f21b36a2", "guid": "bfdfe7dc352907fc980b868725387e98e37596068807202609d79cb2ba55aed5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc1663c50cb1b6f1ba4d586811887410", "guid": "bfdfe7dc352907fc980b868725387e982a24f1e0bbaf7b1c382a749189fd5a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b3aa0c8e8f94149e4e3b53181bbe2e0", "guid": "bfdfe7dc352907fc980b868725387e98291c98adafcf720dc059b598652feed3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805803bc8dba454d38f1ca2d8caebfced", "guid": "bfdfe7dc352907fc980b868725387e98da3ffd16c8c55e1057a971bae88f960f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab7f6591fde713196bd4053b4be87e0b", "guid": "bfdfe7dc352907fc980b868725387e98bbedcb255feb58c323feb2daecd49017"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871cc978747c816e6cb87963e1f871573", "guid": "bfdfe7dc352907fc980b868725387e982bc5b7a4bd8bffbaf88000e0d1c41aa0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dba4654a017b93652ab38dde5c0b8e40", "guid": "bfdfe7dc352907fc980b868725387e984f1eaee53b85a84757415d4f3687b072"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ba7c0d5d179a694e42e3e6297b709ea", "guid": "bfdfe7dc352907fc980b868725387e98e8fb4038ddb210e4cf2f3bf652ca35b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eb55be4619ed869e38b592f751c1cb8", "guid": "bfdfe7dc352907fc980b868725387e98c11013a76a3f32b4a6f3c139df154ba5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6936a4edc986a2aba6fb038758a4772", "guid": "bfdfe7dc352907fc980b868725387e98c160e535e636b02b303ffc76bb55326d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9488389292f44a22694ea6f34b63816", "guid": "bfdfe7dc352907fc980b868725387e981143270fa94ec29c7606c377fd0c52e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fb0228242316bcff807a93397e40e09", "guid": "bfdfe7dc352907fc980b868725387e98d0f80349bf88b82b4238697d2c8d9e10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858b10a758ee456cace3588563b7d0681", "guid": "bfdfe7dc352907fc980b868725387e98094dee4e8f8e070db5a6c78dca5e0196"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8ed56e46eccddad911e7ea969ca161b", "guid": "bfdfe7dc352907fc980b868725387e9834b12631d9c017d1ddbf947466eb803d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b9732a048f02ac03affb4b4fa7b710bf", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fadfbda42ce517e5dc36e9130c1ecaa9", "guid": "bfdfe7dc352907fc980b868725387e98db6db7db22361c5e7722392e4da662e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98009d1fe20a6327908de76488787b8a20", "guid": "bfdfe7dc352907fc980b868725387e9833172d33eb152470227d7e3b6e29ee41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a4b85c6c0fd04d6c10eaf4f8c69c967", "guid": "bfdfe7dc352907fc980b868725387e9890bef61d01ebbc3288e29900a1550ec8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810b890100ea1eb848e61b3e6ffc845fc", "guid": "bfdfe7dc352907fc980b868725387e982f36a8622f54a9ec15c237f59f90a7b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfce590e66d4b12ad9537fb2b09f796e", "guid": "bfdfe7dc352907fc980b868725387e98618aced79242a7dc8ad06aa7096a801b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dc38d5b28015a8ac67ae14c782232e6", "guid": "bfdfe7dc352907fc980b868725387e9870abbf190b9432e6d59743e3f484e9dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832d50528570d23378640418d97a5acde", "guid": "bfdfe7dc352907fc980b868725387e9885bf7270d735b93ee1aed22225e7e061"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3261cf976ade8dfb8ad9467c70d54cb", "guid": "bfdfe7dc352907fc980b868725387e98ba23840950fde28138b9c2a84befe6b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad973d5554895a85efa503cc8b63f44e", "guid": "bfdfe7dc352907fc980b868725387e9817799b5b069e0a5378b2f1de65caf8eb"}], "guid": "bfdfe7dc352907fc980b868725387e98964da8c41b24636d9d338de21fb08fe2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e5846d5481396a0e80acb6050a58602a", "guid": "bfdfe7dc352907fc980b868725387e98015c062afdb30ba698f98b6ea63a1d36"}], "guid": "bfdfe7dc352907fc980b868725387e9856f337a0270b8ff9a09062fba0a9859f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98301a0f773483e7a62c7a59d2ce137d3c", "targetReference": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8"}], "guid": "bfdfe7dc352907fc980b868725387e9877cd28b09d57fbb5f9abe0ec343ccf66", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8", "name": "sqflite_darwin-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98dbbec3eebed26c79cc653713be723aba", "name": "sqflite_darwin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}