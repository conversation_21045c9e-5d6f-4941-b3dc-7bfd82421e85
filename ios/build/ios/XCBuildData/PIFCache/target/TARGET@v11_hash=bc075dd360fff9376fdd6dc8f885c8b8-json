{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98add7228b85ff615243d752026d28c633", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SwiftyBeaver", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "IBSC_MODULE": "SwiftyBeaver", "INFOPLIST_FILE": "Target Support Files/SwiftyBeaver/ResourceBundle-SwiftyBeaver-SwiftyBeaver-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.4", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "SwiftyBeaver", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98b7e55c414dfbfb22c928ed474aa63c2b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985cec5f90735e9bbdd21c8c3b18900666", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SwiftyBeaver", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "IBSC_MODULE": "SwiftyBeaver", "INFOPLIST_FILE": "Target Support Files/SwiftyBeaver/ResourceBundle-SwiftyBeaver-SwiftyBeaver-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.4", "PRODUCT_NAME": "SwiftyBeaver", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e981428b1fa2b5ff0e72c356a7825ed8afe", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985cec5f90735e9bbdd21c8c3b18900666", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SwiftyBeaver", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_REMINDERS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_BLUETOOTH=1", "IBSC_MODULE": "SwiftyBeaver", "INFOPLIST_FILE": "Target Support Files/SwiftyBeaver/ResourceBundle-SwiftyBeaver-SwiftyBeaver-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.4", "PRODUCT_NAME": "SwiftyBeaver", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a9a1d28cad14c7a65bf8147728a6d44f", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e02471299e76ed738504d22c99d08c5e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9895d97f0e6a179822b223c38c5666df2c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e34cabcbb5259d81d1b4a7e73725f56", "guid": "bfdfe7dc352907fc980b868725387e98a2cb6252981804b98154f0afd1159086"}], "guid": "bfdfe7dc352907fc980b868725387e98ce342bb5f0b3478fae0083837799f110", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e989ef56b0808e8d339e3227087886f9779", "name": "SwiftyBeaver-SwiftyBeaver", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab9be2fc12017c90d2dee8ff5fbd4e69", "name": "SwiftyBeaver.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}