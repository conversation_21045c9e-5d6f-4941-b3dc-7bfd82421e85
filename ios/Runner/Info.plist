<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Go Control</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Go Control</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FLTEnableImpeller</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>sms</string>
		<string>tel</string>
		<string>https</string>
		<string>http</string>
		<string>spotify</string>
		<string>tidal</string>
		<string>googlehome</string>
		<string>chromecast</string>
		<string>comgooglecast</string>
		<string>custom-scheme</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>This app will discover, connect and control the dedicated device with Bluetooth LE，This function must be enabled</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>This app will discover, connect and control the dedicated device with Bluetooth LE，This function must be enabled</string>
	<key>NSBonjourServices</key>
	<array>
		<string>_linkplay._tcp</string>
		<string>_http._tcp</string>
		<string>_mymei._tcp.local</string>
		<string>_dartobservatory._tcp</string>
		<string>_upnp._tcp</string>
    	<string>_ssdp._udp</string>
	</array>
	<key>NSCalendarsUsageDescription</key>
	<string>This app requires access to your calendar to provide reminders and schedule events.</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>Need to use network services to find nearby devices</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Need access to location information to search for nearby devices</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Need access to location information to search for nearby devices</string>
	<key>NSNetworkVolumesUsageDescription</key>
	<string>Must have network permission to connect network devices, otherwise you cannot search for nearby network devices</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>bluetooth-central</string>
		<string>bluetooth-peripheral</string>
	</array>
	<key>UILaunchScreen</key>
	<dict>
		<key>UIColorName</key>
		<string>background</string>
		<key>UIImageName</key>
		<string>LaunchImage</string>
	</dict>
	<key>UILaunchStoryboardName</key>
	<string>Launch Screen.storyboard</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>com.apple.developer.device-information</key>
	<dict>
		<key>user-assigned-device-name</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>need requires access to media library</string>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
</dict>
</plist>
